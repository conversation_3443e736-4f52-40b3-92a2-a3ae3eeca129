-- License user data from Genesys Cloud /api/v2/license/users endpoint
-- NOTE: This API only provides userid, licensename, and selfUri
-- The following fields will always be NULL due to API limitations:
-- username, email, licensetype, licenseassigneddate, licenseexpirydate, licensestatus, organizationid, divisionid

CREATE TABLE IF NOT EXISTS licenseuserdata (
    keyid varchar(100) NOT NULL,
    userid varchar(50),                         -- From API: user ID
    username varchar(200),                      -- Always NULL - not provided by API
    email varchar(200),                         -- Always NULL - not provided by API
    licensename varchar(200),                   -- From API: license name
    licensetype varchar(100),                   -- Always NULL - not provided by API
    licenseassigneddate timestamp without time zone,  -- Always NULL - not provided by API
    licenseexpirydate timestamp without time zone,    -- Always NULL - not provided by API
    licensestatus varchar(50),                  -- Always NULL - not provided by API
    organizationid varchar(50),                 -- Always NULL - not provided by API
    divisionid varchar(50),                     -- Always NULL - not provided by API
    updated timestamp without time zone,        -- Populated by adapter
    CONSTRAINT licenseuserdata_pkey PRIMARY KEY (keyid)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS licenseuserdata_userid ON licenseuserdata (userid);
CREATE INDEX IF NOT EXISTS licenseuserdata_licensename ON licenseuserdata (licensename);
CREATE INDEX IF NOT EXISTS licenseuserdata_updated ON licenseuserdata (updated);
