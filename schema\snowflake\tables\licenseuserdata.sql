CREATE TABLE IF NOT EXISTS licenseuserdata (
    keyid varchar(100) NOT NULL,
    userid varchar(50),
    username varchar(200),
    email varchar(200),
    licensename varchar(200),
    licensetype varchar(100),
    licenseassigneddate timestamp without time zone,
    licenseexpirydate timestamp without time zone,
    licensestatus varchar(50),
    organizationid varchar(50),
    divisionid varchar(50),
    updated timestamp without time zone,
    CONSTRAINT licenseuserdata_pkey PRIMARY KEY (keyid)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS licenseuserdata_userid ON licenseuserdata (userid);
CREATE INDEX IF NOT EXISTS licenseuserdata_licensename ON licenseuserdata (licensename);
CREATE INDEX IF NOT EXISTS licenseuserdata_updated ON licenseuserdata (updated);
