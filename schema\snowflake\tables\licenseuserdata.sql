CREATE TABLE IF NOT EXISTS licenseuserdata (
    keyid varchar(100) NOT NULL,
    userid varchar(50),
    licensename varchar(200),
    updated timestamp without time zone,
    CONSTRAINT licenseuserdata_pkey PRIMARY KEY (keyid)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS licenseuserdata_userid ON licenseuserdata (userid);
CREATE INDEX IF NOT EXISTS licenseuserdata_licensename ON licenseuserdata (licensename);
CREATE INDEX IF NOT EXISTS licenseuserdata_updated ON licenseuserdata (updated);
