CREATE TABLE IF NOT EXISTS suboverviewdata (
    keyid varchar(200) NOT NULL,
    rowdate timestamp without time zone,
    startdate timestamp without time zone,
    enddate timestamp without time zone,
    licname varchar(200),
    partnumber varchar(50),
    "grouping" varchar(50),
    unitofmeasuretype varchar(50),
    usagequantity numeric(20, 2),
    prepayQuantity numeric(20, 2),
    overageprice numeric(20, 2),
    iscancellable bit(1),
    bundlequantity integer,
    isthirdparty bit(1),
    updated timestamp without time zone,
    CONSTRAINT suboverviewdata_pkey PRIMARY KEY (keyid)
) TABLESPACE pg_default;

ALTER TABLE suboverviewData
ADD column IF NOT exists startdate timestamp without time zone;

ALTER TABLE suboverviewData
ADD column IF NOT exists enddate timestamp without time zone;

ALTER TABLE suboverviewData
ADD column IF NOT exists prepayQuantity numeric(20, 2);

COMMENT ON TABLE suboverviewdata IS 'Table storing suboverview data';

COMMENT ON COLUMN suboverviewdata.keyid IS 'Primary key identifier';
COMMENT ON COLUMN suboverviewdata.rowdate IS 'Date of the row';
COMMENT ON COLUMN suboverviewdata.startdate IS 'Start date of the period';
COMMENT ON COLUMN suboverviewdata.enddate IS 'End date of the period';
COMMENT ON COLUMN suboverviewdata.licname IS 'License name';
COMMENT ON COLUMN suboverviewdata.partnumber IS 'Part number';
COMMENT ON COLUMN suboverviewdata."grouping" IS 'Grouping of the data';
COMMENT ON COLUMN suboverviewdata.unitofmeasuretype IS 'Type of unit of measure';
COMMENT ON COLUMN suboverviewdata.usagequantity IS 'Quantity of usage';
COMMENT ON COLUMN suboverviewdata.prepayQuantity IS 'Quantity of prepay';
COMMENT ON COLUMN suboverviewdata.overageprice IS 'Price for overage';
COMMENT ON COLUMN suboverviewdata.iscancellable IS 'Flag indicating if the item is cancellable';
COMMENT ON COLUMN suboverviewdata.bundlequantity IS 'Quantity in bundle';
COMMENT ON COLUMN suboverviewdata.isthirdparty IS 'Flag indicating if it is a third-party item';
COMMENT ON COLUMN suboverviewdata.updated IS 'Timestamp of last update';

DO $$
BEGIN
    IF EXISTS (
        SELECT 1 
        FROM suboverviewData
        WHERE startdate IS NULL
    ) THEN
        TRUNCATE TABLE suboverviewdata;
        UPDATE tabledefinitions
        SET datekeyfield = CURRENT_DATE - INTERVAL '12 months'
        WHERE tablename = 'suboverviewdata';
    ELSE
        RAISE NOTICE 'No records found with startdate as NULL.';
    END IF;
END $$;

DO
$$
BEGIN
  IF EXISTS (
    SELECT 1
    FROM suboverviewdata
    WHERE NOT (LENGTH(keyid) - LENGTH(REPLACE(keyid, '|', '')) >= 3)
  ) THEN
    -- Perform the DELETE if rows exist
    DELETE
    FROM suboverviewdata
    WHERE NOT (LENGTH(keyid) - LENGTH(REPLACE(keyid, '|', '')) >= 3);
  END IF;
END;
$$;
