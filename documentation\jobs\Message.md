# Message Job Documentation

## Overview

The **Message** job is designed to synchronize message-based conversation data from Genesys Cloud. This job focuses specifically on "message" mediatype interactions, handling SMS, social media messaging, and other asynchronous messaging channels that differ from real-time chat conversations.

## Data Flow Architecture

:::mermaid
graph TB
    subgraph "Genesys Cloud APIs"
        CONV[Conversations API<br/>/api/v2/analytics/conversations]
        DETAILS[Message Details<br/>/api/v2/conversations/{id}]
        MESSAGES[Messages API<br/>/api/v2/conversations/{id}/messages]
        SOCIAL[Social Media APIs<br/>Platform-specific endpoints]
    end
    
    subgraph "Message Job Process"
        MSG[GCUpdateMessageData<br/>Message Controller]
        FILTER[Message Filter<br/>Mediatype: message]
        FETCH[Data Fetcher<br/>Message Conversation Retrieval]
        PROC[Message Processor<br/>Content Analysis]
        PLATFORM[Platform Handler<br/>Channel-specific Processing]
    end
    
    subgraph "Database Tables"
        MD[(messagedata<br/>Message Conversations)]
        MCD[(messagecontentdata<br/>Message Content)]
        MPD[(messageparticipantdata<br/>Message Participants)]
        MAD[(messageattributedata<br/>Message Attributes)]
        MSD[(messagesessiondata<br/>Session Tracking)]
    end
    
    subgraph "Message Channels"
        SMS[SMS Messages<br/>Text Messaging]
        SOCIAL_MEDIA[Social Media<br/>Facebook, Twitter, etc.]
        EMAIL[Email Messages<br/>Email Conversations]
        WHATSAPP[WhatsApp<br/>Business Messaging]
    end
    
    %% Main flow
    MSG --> FILTER
    FILTER --> FETCH
    FETCH --> CONV
    CONV --> PROC
    PROC --> DETAILS
    PROC --> MESSAGES
    PROC --> PLATFORM
    
    %% Channel processing
    PLATFORM --> SMS
    PLATFORM --> SOCIAL_MEDIA
    PLATFORM --> EMAIL
    PLATFORM --> WHATSAPP
    
    %% Database storage
    PROC --> MD
    PROC --> MCD
    PROC --> MPD
    PROC --> MAD
    PROC --> MSD
    
    %% Styling
    classDef api fill:#e1f5fe
    classDef process fill:#e8f5e8
    classDef database fill:#fff3e0
    classDef channel fill:#f3e5f5
    
    class CONV,DETAILS,MESSAGES,SOCIAL api
    class MSG,FILTER,FETCH,PROC,PLATFORM process
    class MD,MCD,MPD,MAD,MSD database
    class SMS,SOCIAL_MEDIA,EMAIL,WHATSAPP channel
:::

## Configuration

### Basic Configuration
```json
{
  "Job": "Message",
  "Preferences": {
    "RenameParticipantAttributeNames": {},
    "MaxSyncSpan": "1.00:00",
    "LookBackSpan": "0.08:00"
  }
}
```

### Message-Specific Configuration

#### Channel-Specific Attribute Mapping
```json
{
  "RenameParticipantAttributeNames": {
    "smsNumber": "customer_phone_number",
    "socialHandle": "social_media_username",
    "emailAddress": "customer_email",
    "whatsappId": "whatsapp_contact_id",
    "messageChannel": "originating_platform",
    "messageType": "content_type"
  }
}
```

#### Platform Settings
- **SMS Processing**: Handle SMS-specific metadata and delivery status
- **Social Media**: Process platform-specific identifiers and context
- **Email Integration**: Handle email threading and attachment references
- **WhatsApp Business**: Process business messaging features

## Database Schema

### Primary Tables

#### messagedata
- **Purpose**: Main message conversation records
- **Key Fields**: conversationid, starttime, endtime, messagechannel, direction
- **Unique Features**: Channel-specific metadata, message threading
- **Indexes**: conversationid (PK), starttime, messagechannel

#### messagecontentdata
- **Purpose**: Individual message content and metadata
- **Key Fields**: conversationid, messageid, timestamp, content, contenttype
- **Features**: Message content, attachments, delivery status
- **Indexes**: conversationid, messageid, timestamp

#### messageparticipantdata
- **Purpose**: Message participants across different channels
- **Key Fields**: conversationid, participantid, channelidentifier, participanttype
- **Features**: Channel-specific participant identifiers
- **Indexes**: conversationid, participantid, channelidentifier

#### messageattributedata
- **Purpose**: Message and channel-specific attributes
- **Key Fields**: conversationid, attributename, attributevalue, channelcontext
- **Features**: Platform-specific metadata, custom attributes
- **Indexes**: conversationid, attributename, channelcontext

#### messagesessiondata
- **Purpose**: Message session and routing information
- **Key Fields**: conversationid, sessionid, channeltype, routingdata
- **Features**: Cross-channel routing, session continuity
- **Indexes**: conversationid, sessionid, channeltype

## API Integration

### Message-Specific API Usage

#### Conversations API with Message Filter
```json
{
  "interval": "2024-01-01T00:00:00Z/2024-01-01T23:59:59Z",
  "granularity": "PT15M",
  "filter": {
    "type": "and",
    "predicates": [
      {
        "type": "dimension",
        "dimension": "mediaType",
        "operator": "matches",
        "value": "message"
      }
    ]
  }
}
```

#### Channel-Specific Endpoints
- **SMS**: Standard conversation APIs with SMS-specific attributes
- **Social Media**: Platform integration APIs for social messaging
- **Email**: Email conversation threading and attachment handling
- **WhatsApp**: WhatsApp Business API integration

## Message Channel Features

### SMS Messaging
- **Phone Number Handling**: Normalize and validate phone numbers
- **Delivery Status**: Track message delivery and read receipts
- **Character Limits**: Handle SMS character limitations and splitting
- **Carrier Information**: Track carrier and delivery network data

### Social Media Messaging
- **Platform Integration**: Facebook Messenger, Twitter DM, Instagram
- **User Identification**: Handle platform-specific user identifiers
- **Rich Media**: Process images, videos, and interactive content
- **Public vs Private**: Distinguish between public posts and private messages

### Email Conversations
- **Threading**: Maintain email conversation threads
- **Attachments**: Reference and catalog email attachments
- **Headers**: Process email headers for routing and metadata
- **HTML Content**: Handle both plain text and HTML email content

### WhatsApp Business
- **Business Features**: Templates, quick replies, interactive messages
- **Media Handling**: Images, documents, voice messages
- **Group Messaging**: Handle group conversation dynamics
- **Business Profiles**: Link to WhatsApp Business account information

## Dependencies

### Prerequisites
- **FactData**: Queue and user reference data
- **Database Schema**: Message-specific tables installed
- **API Permissions**: Conversations and messaging read permissions
- **Channel Configuration**: Platform-specific API access

### Related Jobs
- **Chat**: Real-time chat processing (different from async messaging)
- **Interaction**: General conversation data processing
- **VoiceAnalysis**: Cross-channel analytics and insights
- **Evaluation**: Quality evaluations for message interactions

## Performance Optimization

### Message-Specific Optimizations
- **Channel Batching**: Group messages by channel for efficient processing
- **Content Caching**: Cache frequently accessed message content
- **Platform Throttling**: Respect platform-specific rate limits
- **Asynchronous Processing**: Handle async nature of message conversations

### Content Processing
- **Media Handling**: Efficient processing of multimedia content
- **Text Analysis**: Optimize text processing for large message volumes
- **Attachment Processing**: Handle file attachments and media references
- **Threading Logic**: Efficient conversation thread reconstruction

## Monitoring and Troubleshooting

### Message-Specific Metrics
- **Message Volume**: Number of messages processed by channel
- **Channel Performance**: Processing efficiency per messaging platform
- **Content Processing Rate**: Speed of message content analysis
- **Delivery Tracking**: Message delivery success rates

### Common Message Issues

#### Channel Connectivity
- **Symptoms**: Missing messages from specific channels
- **Causes**: Platform API issues, authentication problems
- **Solution**: Verify channel configurations and API credentials

#### Content Processing Errors
- **Symptoms**: Incomplete message content or missing attachments
- **Causes**: Content type handling issues, encoding problems
- **Solution**: Review content processing logic and encoding handling

#### Threading Issues
- **Symptoms**: Broken conversation threads or duplicate conversations
- **Causes**: Thread identification logic, participant mapping errors
- **Solution**: Validate threading logic and participant consistency

### Message-Specific Logging
- **Channel Processing**: Log processing status for each messaging channel
- **Content Analysis**: Log content processing results and any issues
- **Threading Operations**: Log conversation thread construction and validation

## Usage Examples

### Standard Message Sync
```bash
GenesysAdapter --job Message --config message-production.json
```

### Multi-Channel Message Processing
```json
{
  "Job": "Message",
  "Preferences": {
    "RenameParticipantAttributeNames": {
      "smsNumber": "phone",
      "facebookId": "fb_user_id",
      "twitterHandle": "twitter_username",
      "emailAddr": "email_address"
    }
  }
}
```

### High-Frequency Message Sync
```json
{
  "Job": "Message",
  "Preferences": {
    "MaxSyncSpan": "0.15:00",
    "LookBackSpan": "0.02:00"
  }
}
```

## Best Practices

### Message Data Management
- **Channel Separation**: Maintain clear separation between messaging channels
- **Content Retention**: Implement appropriate retention policies for message content
- **Privacy Compliance**: Ensure message data handling meets privacy regulations

### Cross-Channel Analytics
- **Unified Reporting**: Enable cross-channel message analytics
- **Customer Journey**: Track customer interactions across messaging platforms
- **Performance Comparison**: Compare effectiveness across messaging channels

### Integration Considerations
- **Platform Updates**: Stay current with messaging platform API changes
- **Scalability**: Design for varying message volumes across channels
- **Reliability**: Implement robust error handling for platform connectivity issues
