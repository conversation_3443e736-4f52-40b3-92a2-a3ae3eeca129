# Interaction Job Documentation

## Overview

The **Interaction** job is one of the core synchronization jobs in the Genesys Adapter, responsible for retrieving detailed conversation data from Genesys Cloud. This job collects comprehensive interaction information including conversation summaries, participant details, attributes, and media-specific data for voice, chat, email, and other communication channels.

## Data Flow Architecture

:::mermaid
graph TB
    subgraph "Genesys Cloud APIs"
        CONV[Conversations API<br/>/api/v2/analytics/conversations]
        DETAILS[Conversation Details<br/>/api/v2/conversations/{id}]
        PARTICIPANTS[Participants API<br/>/api/v2/conversations/{id}/participants]
        ATTRIBUTES[Attributes API<br/>/api/v2/conversations/{id}/attributes]
    end
    
    subgraph "Interaction Job Process"
        INT[GCUpdateInteractionData<br/>Main Controller]
        FETCH[Data Fetcher<br/>Conversation Retrieval]
        PROC[Data Processor<br/>Conversation Analysis]
        ATTR[Attribute Processor<br/>Custom Data Handling]
        DIFF[Diffing Engine<br/>Change Detection]
    end
    
    subgraph "Database Tables"
        CD[(conversationdata<br/>Main Conversation Records)]
        PD[(participantdata<br/>Participant Information)]
        SD[(segmentdata<br/>Media Segment Details)]
        AD[(attributedata<br/>Custom Attributes)]
        MED[(mediaendpointstatdata<br/>Media Statistics)]
    end
    
    subgraph "Configuration Options"
        BACKFILL[Backfill Mode<br/>Historical Data Sync]
        BLOCK[Block Attributes<br/>Privacy Controls]
        RENAME[Rename Attributes<br/>Field Mapping]
        RATE[Rate Limiting<br/>API Throttling]
    end
    
    %% Main flow
    INT --> FETCH
    FETCH --> CONV
    CONV --> PROC
    PROC --> DETAILS
    PROC --> PARTICIPANTS
    PROC --> ATTRIBUTES
    ATTRIBUTES --> ATTR
    ATTR --> DIFF
    DIFF --> CD
    DIFF --> PD
    DIFF --> SD
    DIFF --> AD
    DIFF --> MED
    
    %% Configuration connections
    BACKFILL -.-> FETCH
    BLOCK -.-> ATTR
    RENAME -.-> ATTR
    RATE -.-> FETCH
    
    %% Styling
    classDef api fill:#e1f5fe
    classDef process fill:#e8f5e8
    classDef database fill:#fff3e0
    classDef config fill:#f3e5f5
    
    class CONV,DETAILS,PARTICIPANTS,ATTRIBUTES api
    class INT,FETCH,PROC,ATTR,DIFF process
    class CD,PD,SD,AD,MED database
    class BACKFILL,BLOCK,RENAME,RATE config
:::

## Configuration

### Basic Configuration
```json
{
  "Job": "Interaction",
  "Preferences": {
    "Backfill": false,
    "BlockParticipantAttributes": [],
    "RenameParticipantAttributeNames": {},
    "RateLimiting": true,
    "MaxSyncSpan": "1.00:00",
    "LookBackSpan": "0.08:00"
  }
}
```

### Advanced Configuration Options

#### Backfill Mode
- **Purpose**: Synchronize historical conversation data
- **Default**: `false`
- **Usage**: Set to `true` for initial data loads or historical analysis

#### Attribute Blocking
```json
{
  "BlockParticipantAttributes": [
    "sensitiveField1",
    "personalData",
    "confidentialInfo"
  ]
}
```

#### Attribute Renaming
```json
{
  "RenameParticipantAttributeNames": {
    "originalFieldName": "newFieldName",
    "customerID": "customer_identifier",
    "agentNotes": "agent_comments"
  }
}
```

## Database Schema

### Primary Tables

#### conversationdata
- **Purpose**: Main conversation records with summary information
- **Key Fields**: conversationid, starttime, endtime, direction, mediatype
- **Indexes**: conversationid (PK), starttime, endtime, mediatype

#### participantdata  
- **Purpose**: Individual participant information for each conversation
- **Key Fields**: conversationid, participantid, purpose, starttime, endtime
- **Indexes**: conversationid, participantid, purpose

#### segmentdata
- **Purpose**: Media segment details for different communication channels
- **Key Fields**: conversationid, segmentid, segmenttype, starttime, endtime
- **Indexes**: conversationid, segmentid, segmenttype

#### attributedata
- **Purpose**: Custom conversation and participant attributes
- **Key Fields**: conversationid, attributename, attributevalue
- **Indexes**: conversationid, attributename

#### mediaendpointstatdata
- **Purpose**: Media quality and performance statistics
- **Key Fields**: conversationid, participantid, codec, jitter, latency
- **Indexes**: conversationid, participantid

## API Integration

### Primary Endpoints

#### Conversations Analytics API
- **Endpoint**: `/api/v2/analytics/conversations`
- **Method**: POST
- **Purpose**: Retrieve conversation summaries with filters
- **Rate Limits**: 300 requests per token

#### Conversation Details API
- **Endpoint**: `/api/v2/conversations/{conversationId}`
- **Method**: GET  
- **Purpose**: Get detailed conversation information
- **Rate Limits**: Standard API limits apply

#### Participants API
- **Endpoint**: `/api/v2/conversations/{conversationId}/participants`
- **Method**: GET
- **Purpose**: Retrieve participant details and attributes
- **Rate Limits**: Standard API limits apply

### Query Parameters
- **interval**: Date range for conversation retrieval
- **granularity**: Time granularity (PT15M, PT30M, PT1H)
- **groupBy**: Grouping criteria for aggregation
- **filter**: Conversation filtering criteria

## Dependencies

### Prerequisites
- **FactData**: User and queue reference data must be current
- **Database Schema**: All interaction tables must be installed
- **API Permissions**: Analytics and conversations read permissions

### Related Jobs
- **VoiceAnalysis**: Processes voice-specific analytics from interaction data
- **Chat**: Specialized chat conversation processing
- **Message**: Message-specific interaction handling
- **Evaluation**: Quality evaluations linked to conversations

## Performance Optimization

### Batch Processing
- **Default Batch Size**: 1000 conversations
- **Configurable Range**: 100-5000 conversations
- **Memory Optimization**: Streaming processing for large datasets

### Rate Limiting
- **Token Management**: Automatic token refresh every 275 requests
- **Retry Logic**: Exponential backoff for rate limit errors
- **Concurrent Processing**: Limited to 10 concurrent API calls

### Database Optimization
- **Bulk Operations**: Uses bulk insert/update for performance
- **Indexing**: Optimized indexes for common query patterns
- **Partitioning**: Date-based partitioning for large datasets

## Monitoring and Troubleshooting

### Key Metrics
- **Conversations Processed**: Total number of conversations synchronized
- **API Response Times**: Average response time for Genesys Cloud APIs
- **Error Rates**: Percentage of failed API calls or data processing errors
- **Data Freshness**: Time lag between conversation occurrence and synchronization

### Common Issues

#### Rate Limiting Errors
- **Symptoms**: HTTP 429 responses from Genesys Cloud
- **Solution**: Reduce batch size or increase delay between requests
- **Prevention**: Enable rate limiting configuration

#### Missing Conversations
- **Symptoms**: Gaps in conversation data
- **Causes**: API permissions, date range issues, or filtering problems
- **Solution**: Verify permissions and adjust query parameters

#### Attribute Processing Errors
- **Symptoms**: Missing or incorrectly mapped attributes
- **Causes**: Attribute blocking/renaming configuration issues
- **Solution**: Review attribute configuration and field mappings

### Logging and Diagnostics
- **Log Level**: Set to Debug for detailed API interaction logging
- **Performance Logs**: Batch processing timing and throughput metrics
- **Error Logs**: Detailed error information with conversation IDs

## Usage Examples

### Standard Daily Sync
```bash
GenesysAdapter --job Interaction --config production.json
```

### Historical Backfill
```bash
GenesysAdapter --job Interaction --backfill true --config backfill.json
```

### Custom Date Range
```json
{
  "Job": "Interaction",
  "Preferences": {
    "MaxSyncSpan": "7.00:00",
    "LookBackSpan": "1.00:00"
  }
}
```

## Best Practices

### Scheduling
- **Frequency**: Every 15-30 minutes for near real-time data
- **Off-Peak Processing**: Schedule large backfills during low-usage periods
- **Incremental Updates**: Use small time windows for regular synchronization

### Data Quality
- **Validation**: Implement data validation rules for critical fields
- **Monitoring**: Set up alerts for data quality issues
- **Reconciliation**: Regular comparison with Genesys Cloud data

### Security
- **Attribute Filtering**: Block sensitive personal information
- **Access Control**: Limit database access to authorized users
- **Audit Trail**: Maintain logs of data access and modifications
