# Realtime Job Documentation

## Overview

The Realtime job provides continuous monitoring of queue and user metrics through WebSocket connections to Genesys Cloud. It delivers live operational data for contact center dashboards and real-time reporting.

## Data Flow Diagram

:::mermaid
graph TB
    subgraph "Genesys Cloud"
        AUTH[OAuth Authentication<br/>/api/v2/oauth/token]
        TOPICS[Available Topics<br/>/api/v2/notifications/availabletopics]
        CHANNELS[WebSocket Channels<br/>/api/v2/notifications/channels]
        WS[WebSocket Connection<br/>Real-time Events]
    end

    subgraph "Realtime Job Process"
        RT[GCRealTime<br/>Main Controller]
        CONN[Connection Manager<br/>WebSocket Handling]
        PROC[Event Processor<br/>Data Transformation]
        BATCH[Batch Writer<br/>Database Updates]
    end

    subgraph "Database Tables"
        QRT[(queuerealtimedata<br/>Live Queue Metrics)]
        URT[(userrealtimedata<br/>Live User Status)]
        CRT[(queuerealtimeconvdata<br/>Live Conversations)]
    end

    subgraph "Configuration"
        LIMITS[Rate Limits<br/>1000 topics/channel<br/>20 channels/client]
        TOPICS_CONFIG[Topic Subscriptions<br/>Queue & User Events]
        RETRY[Retry Logic<br/>Connection Recovery]
    end

    %% Flow connections
    AUTH --> RT
    RT --> TOPICS
    RT --> CHANNELS
    CHANNELS --> WS
    WS --> CONN
    CONN --> PROC
    PROC --> BATCH

    BATCH --> QRT
    BATCH --> URT
    BATCH --> CRT

    %% Configuration connections
    LIMITS -.-> RT
    TOPICS_CONFIG -.-> RT
    RETRY -.-> CONN

    %% Styling
    classDef api fill:#e1f5fe
    classDef process fill:#e8f5e8
    classDef database fill:#fff3e0
    classDef config fill:#f3e5f5

    class AUTH,TOPICS,CHANNELS,WS api
    class RT,CONN,PROC,BATCH process
    class QRT,URT,CRT database
    class LIMITS,TOPICS_CONFIG,RETRY config
:::

## Configuration

### Required Settings
- **GenesysApi**: OAuth credentials and region
- **Database**: Connection string for target database
- **Topics**: Queue and user IDs to monitor

### Optional Settings
- **ChannelCount**: Number of WebSocket channels (default: auto-calculated)
- **BatchSize**: Records per database write (default: 1000)
- **RetryAttempts**: Connection retry limit (default: 5)

### Example Configuration
```json
{
  "Job": "Realtime",
  "GenesysApi": {
    "ClientId": "your-client-id",
    "ClientSecret": "your-client-secret",
    "Region": "us-east-1"
  },
  "Database": {
    "ConnectionString": "your-connection-string"
  }
}
```

## Database Tables

### queuerealtimedata
- **Purpose**: Live queue metrics and statistics
- **Key Fields**: queueid, timestamp, offered, answered, abandoned
- **Update Frequency**: Every 30 seconds

### userrealtimedata  
- **Purpose**: Live user presence and status
- **Key Fields**: userid, timestamp, presenceid, routingstatus
- **Update Frequency**: On status change

### queuerealtimeconvdata
- **Purpose**: Live conversation metrics per queue
- **Key Fields**: queueid, timestamp, activeconversations, longestwait
- **Update Frequency**: Every 30 seconds

## API Endpoints

- `/api/v2/oauth/token` - Authentication
- `/api/v2/notifications/availabletopics` - Topic discovery
- `/api/v2/notifications/channels` - Channel management
- WebSocket endpoint for real-time events

## Dependencies

### Prerequisites
- Valid OAuth credentials with real-time permissions
- Database schema installed
- Network connectivity for WebSocket connections

### Related Jobs
- **Users**: Provides user IDs for monitoring
- **QueueMembership**: Provides queue IDs for monitoring
- **FactData**: Provides reference data for lookups

## Monitoring

### Key Metrics
- **Connection Status**: WebSocket connection health
- **Event Rate**: Events processed per minute
- **Lag Time**: Delay between event and database write
- **Error Rate**: Failed events or connection drops

### Logging
- Connection establishment and drops
- Topic subscription status
- Batch processing statistics
- Error conditions and retries

## Troubleshooting

### Common Issues

#### WebSocket Connection Drops
- **Symptoms**: Missing real-time data, connection errors
- **Causes**: Network issues, token expiration, rate limiting
- **Solutions**: Check network connectivity, refresh tokens, reduce topic count

#### High Memory Usage
- **Symptoms**: Application memory growth, slow performance
- **Causes**: Large batch sizes, slow database writes
- **Solutions**: Reduce batch size, optimize database performance

#### Missing Data
- **Symptoms**: Gaps in real-time data
- **Causes**: Topic subscription failures, processing errors
- **Solutions**: Verify topic permissions, check error logs

### Error Codes
- **401**: Authentication failure - check credentials
- **429**: Rate limit exceeded - reduce topic count
- **503**: Service unavailable - retry with backoff

## Performance Optimization

### Best Practices
- Monitor topic count per channel (max 1000)
- Use appropriate batch sizes for database writes
- Implement proper error handling and retries
- Monitor WebSocket connection health

### Scaling Considerations
- Large organizations may need multiple channels
- Consider database write performance for high-volume environments
- Monitor network bandwidth for WebSocket traffic

## Examples

### Basic Usage
```bash
GenesysAdapter --job Realtime
```

### With Custom Configuration
```bash
GenesysAdapter --job Realtime --config realtime-config.json
```

### Monitoring Specific Queues
```json
{
  "Job": "Realtime",
  "Preferences": {
    "QueueIds": ["queue-id-1", "queue-id-2"]
  }
}
```
