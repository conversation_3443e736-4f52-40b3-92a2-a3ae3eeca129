-- Only create the subuserusagedata table if it doesn't exist and we're not in deprecation mode
-- Since this table is deprecated, we don't create it for new installations
DO $$
BEGIN
    -- Check if table exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'subuserusagedata') THEN
        -- Table exists, add deprecation columns for existing installations
        ALTER TABLE subuserusagedata
        ADD COLUMN IF NOT EXISTS deprecated_status varchar(50) DEFAULT 'deprecated';

        ALTER TABLE subuserusagedata
        ADD COLUMN IF NOT EXISTS deprecated_date timestamp without time zone DEFAULT '2025-03-10 00:00:00';

        RAISE NOTICE 'subuserusagedata table exists - added deprecation tracking columns';
    ELSE
        -- Table doesn't exist and is deprecated, so don't create it
        RAISE NOTICE 'subuserusagedata table does not exist and is deprecated - skipping table creation';
    END IF;
END $$;

COMMENT ON COLUMN subuserusageData.date IS 'Date';
COMMENT ON COLUMN subuserusageData.keyid IS 'Primary Key';
COMMENT ON COLUMN subuserusageData.licensename IS 'License Used';
COMMENT ON COLUMN subuserusageData.secs IS 'Time in Secs';
COMMENT ON COLUMN subuserusageData.updated IS 'Date Row Updated (UTC)';
COMMENT ON COLUMN subuserusageData.userlogin IS 'User GUID';
COMMENT ON COLUMN subuserusageData.hoursstr IS ' ';
COMMENT ON COLUMN subuserusageData.deprecated_status IS 'Deprecation status of the table (active/deprecated)';
COMMENT ON COLUMN subuserusageData.deprecated_date IS 'Date when the underlying API was deprecated (2025-03-10)';
COMMENT ON TABLE subuserusageData IS 'DEPRECATED: Subscription Detailed Data - No longer updated due to Genesys Cloud API deprecation (March 10, 2025). Historical data preserved for reference.';