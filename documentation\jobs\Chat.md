# Chat Job Documentation

## Overview

The **Chat** job specializes in synchronizing chat conversation data from Genesys Cloud. This job focuses specifically on chat mediatype interactions, providing detailed analytics and conversation flow data for chat-based customer interactions including web chat, messaging, and social media conversations.

## Data Flow Architecture

:::mermaid
graph TB
    subgraph "Genesys Cloud APIs"
        CONV[Conversations API<br/>/api/v2/analytics/conversations]
        DETAILS[Chat Details<br/>/api/v2/conversations/{id}]
        MESSAGES[Messages API<br/>/api/v2/conversations/{id}/messages]
        PARTICIPANTS[Participants API<br/>/api/v2/conversations/{id}/participants]
    end
    
    subgraph "Chat Job Process"
        CHAT[GCUpdateChatData<br/>Chat Controller]
        FILTER[Chat Filter<br/>Mediatype: chat]
        FETCH[Data Fetcher<br/>Chat Conversation Retrieval]
        PROC[Chat Processor<br/>Message Analysis]
        ATTR[Attribute Handler<br/>Chat-specific Attributes]
    end
    
    subgraph "Database Tables"
        CD[(chatdata<br/>Chat Conversations)]
        CMD[(chatmessagedata<br/>Individual Messages)]
        CPD[(chatparticipantdata<br/>Chat Participants)]
        CAD[(chatattributedata<br/>Chat Attributes)]
        CSD[(chatsessiondata<br/>Session Information)]
    end
    
    subgraph "Chat Features"
        TRANSCRIPT[Transcript Processing<br/>Message Sequencing]
        SENTIMENT[Sentiment Analysis<br/>Message Tone Detection]
        ROUTING[Routing Analysis<br/>Queue Assignment Logic]
        TRANSFER[Transfer Tracking<br/>Agent Handoffs]
    end
    
    %% Main flow
    CHAT --> FILTER
    FILTER --> FETCH
    FETCH --> CONV
    CONV --> PROC
    PROC --> DETAILS
    PROC --> MESSAGES
    PROC --> PARTICIPANTS
    PARTICIPANTS --> ATTR
    
    %% Processing features
    PROC --> TRANSCRIPT
    PROC --> SENTIMENT
    PROC --> ROUTING
    PROC --> TRANSFER
    
    %% Database storage
    ATTR --> CD
    TRANSCRIPT --> CMD
    ATTR --> CPD
    ATTR --> CAD
    ROUTING --> CSD
    
    %% Styling
    classDef api fill:#e1f5fe
    classDef process fill:#e8f5e8
    classDef database fill:#fff3e0
    classDef feature fill:#f3e5f5
    
    class CONV,DETAILS,MESSAGES,PARTICIPANTS api
    class CHAT,FILTER,FETCH,PROC,ATTR process
    class CD,CMD,CPD,CAD,CSD database
    class TRANSCRIPT,SENTIMENT,ROUTING,TRANSFER feature
:::

## Configuration

### Basic Configuration
```json
{
  "Job": "Chat",
  "Preferences": {
    "RenameParticipantAttributeNames": {},
    "MaxSyncSpan": "1.00:00",
    "LookBackSpan": "0.08:00"
  }
}
```

### Chat-Specific Options

#### Attribute Renaming for Chat
```json
{
  "RenameParticipantAttributeNames": {
    "customerName": "customer_display_name",
    "chatQueue": "assigned_queue",
    "chatSubject": "conversation_topic",
    "customerEmail": "customer_email_address",
    "chatRating": "customer_satisfaction_score"
  }
}
```

#### Message Processing Options
- **Include System Messages**: Process automated system messages
- **Message Ordering**: Maintain chronological message sequence
- **Participant Tracking**: Track all chat participants including bots

## Database Schema

### Primary Tables

#### chatdata
- **Purpose**: Main chat conversation records
- **Key Fields**: conversationid, starttime, endtime, queueid, direction
- **Unique Features**: Chat-specific metrics like message count, response times
- **Indexes**: conversationid (PK), starttime, queueid

#### chatmessagedata
- **Purpose**: Individual chat messages within conversations
- **Key Fields**: conversationid, messageid, timestamp, participantid, messagetext
- **Features**: Message sequencing, sender identification, message types
- **Indexes**: conversationid, messageid, timestamp

#### chatparticipantdata
- **Purpose**: Chat participants including customers, agents, and bots
- **Key Fields**: conversationid, participantid, participanttype, jointime, leavetime
- **Features**: Participant role tracking, session duration
- **Indexes**: conversationid, participantid, participanttype

#### chatattributedata
- **Purpose**: Chat-specific attributes and metadata
- **Key Fields**: conversationid, attributename, attributevalue, participantid
- **Features**: Custom chat attributes, participant-specific data
- **Indexes**: conversationid, attributename, participantid

#### chatsessiondata
- **Purpose**: Chat session information and routing details
- **Key Fields**: conversationid, sessionid, routingmethod, queuewaittime
- **Features**: Routing analytics, queue performance metrics
- **Indexes**: conversationid, sessionid, routingmethod

## API Integration

### Chat-Specific API Usage

#### Conversations API with Chat Filter
```json
{
  "interval": "2024-01-01T00:00:00Z/2024-01-01T23:59:59Z",
  "granularity": "PT15M",
  "filter": {
    "type": "and",
    "predicates": [
      {
        "type": "dimension",
        "dimension": "mediaType",
        "operator": "matches",
        "value": "chat"
      }
    ]
  }
}
```

#### Message Retrieval
- **Endpoint**: `/api/v2/conversations/{conversationId}/messages`
- **Purpose**: Retrieve complete chat transcript
- **Pagination**: Handles large chat conversations with multiple pages

#### Chat Participants
- **Endpoint**: `/api/v2/conversations/{conversationId}/participants`
- **Purpose**: Get participant details including customer and agent information
- **Features**: Participant attributes, join/leave times, roles

## Chat-Specific Features

### Message Processing
- **Chronological Ordering**: Messages sorted by timestamp
- **Participant Attribution**: Each message linked to sender
- **Message Types**: Text, system, attachment, emoji handling
- **Thread Management**: Support for threaded conversations

### Routing Analytics
- **Queue Assignment**: Track which queue handled the chat
- **Wait Time Analysis**: Customer wait time before agent assignment
- **Transfer Tracking**: Agent-to-agent and queue-to-queue transfers
- **Escalation Paths**: Track conversation escalations

### Performance Metrics
- **Response Times**: Agent response time to customer messages
- **Resolution Time**: Total time to resolve chat conversation
- **Message Volume**: Number of messages per conversation
- **Participant Count**: Number of participants in chat session

## Dependencies

### Prerequisites
- **FactData**: Queue and user reference data
- **Database Schema**: Chat-specific tables installed
- **API Permissions**: Conversations and messaging read permissions

### Related Jobs
- **Interaction**: General conversation data (may overlap)
- **Message**: Message-specific processing (different mediatype)
- **VoiceAnalysis**: Cross-channel analytics comparison
- **Evaluation**: Quality evaluations for chat interactions

## Performance Optimization

### Chat-Specific Optimizations
- **Message Batching**: Process messages in conversation groups
- **Participant Caching**: Cache participant data to reduce API calls
- **Attribute Filtering**: Process only relevant chat attributes
- **Incremental Updates**: Only sync new or modified chat data

### Memory Management
- **Streaming Processing**: Handle large chat transcripts efficiently
- **Message Chunking**: Break large conversations into manageable chunks
- **Garbage Collection**: Proper cleanup of processed message data

## Monitoring and Troubleshooting

### Chat-Specific Metrics
- **Chat Volume**: Number of chat conversations processed
- **Message Processing Rate**: Messages processed per minute
- **Transcript Completeness**: Percentage of complete chat transcripts
- **Participant Data Quality**: Accuracy of participant information

### Common Chat Issues

#### Incomplete Transcripts
- **Symptoms**: Missing messages in chat conversations
- **Causes**: API pagination issues, message retrieval timeouts
- **Solution**: Implement robust pagination handling and retry logic

#### Participant Mapping Errors
- **Symptoms**: Incorrect participant attribution for messages
- **Causes**: Participant ID changes, role assignment issues
- **Solution**: Validate participant data consistency

#### Attribute Processing Issues
- **Symptoms**: Missing or incorrect chat attributes
- **Causes**: Attribute renaming configuration, data type mismatches
- **Solution**: Review attribute mapping and data validation

### Chat-Specific Logging
- **Message Processing**: Log message count and processing time per conversation
- **Participant Tracking**: Log participant join/leave events
- **Attribute Mapping**: Log attribute renaming and filtering actions

## Usage Examples

### Standard Chat Sync
```bash
GenesysAdapter --job Chat --config chat-production.json
```

### Chat with Custom Attributes
```json
{
  "Job": "Chat",
  "Preferences": {
    "RenameParticipantAttributeNames": {
      "customerType": "customer_segment",
      "chatSource": "originating_channel",
      "priority": "conversation_priority"
    }
  }
}
```

### High-Volume Chat Processing
```json
{
  "Job": "Chat",
  "Preferences": {
    "MaxSyncSpan": "0.30:00",
    "LookBackSpan": "0.05:00"
  }
}
```

## Best Practices

### Chat Data Management
- **Real-time Processing**: Process chat data frequently for timely insights
- **Message Retention**: Implement appropriate data retention policies
- **Privacy Compliance**: Ensure chat data handling meets privacy requirements

### Performance Tuning
- **Batch Sizing**: Optimize batch sizes for chat conversation volume
- **API Efficiency**: Minimize API calls through intelligent caching
- **Database Indexing**: Ensure proper indexing for chat query patterns

### Quality Assurance
- **Transcript Validation**: Verify chat transcript completeness
- **Participant Verification**: Validate participant data accuracy
- **Attribute Consistency**: Ensure consistent attribute processing
