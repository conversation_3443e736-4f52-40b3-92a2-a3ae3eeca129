using System;
using System.Runtime.Serialization;

namespace GenesysCloudUtils
{
    /// <summary>
    /// Exception thrown when a Genesys Cloud API endpoint has been deprecated.
    /// This exception provides structured information about the deprecated endpoint
    /// to facilitate migration to replacement APIs.
    /// </summary>
    [Serializable]
    public class GenesysApiDeprecatedException : Exception
    {
        /// <summary>
        /// The URL of the deprecated API endpoint
        /// </summary>
        public string EndpointUrl { get; }

        /// <summary>
        /// The HTTP status code returned by the deprecated endpoint
        /// </summary>
        public int HttpStatusCode { get; }

        /// <summary>
        /// The raw API response content from the deprecated endpoint
        /// </summary>
        public string ApiResponse { get; }

        /// <summary>
        /// The date when the API was deprecated (if available)
        /// </summary>
        public DateTime? DeprecationDate { get; }

        /// <summary>
        /// Initializes a new instance of the GenesysApiDeprecatedException class
        /// </summary>
        /// <param name="message">The error message that explains the reason for the exception</param>
        /// <param name="endpointUrl">The URL of the deprecated API endpoint</param>
        /// <param name="httpStatusCode">The HTTP status code returned</param>
        /// <param name="apiResponse">The raw API response content</param>
        /// <param name="deprecationDate">The date when the API was deprecated (optional)</param>
        /// <exception cref="ArgumentNullException">Thrown when message or endpointUrl is null</exception>
        /// <exception cref="ArgumentException">Thrown when message or endpointUrl is empty</exception>
        public GenesysApiDeprecatedException(string message, string endpointUrl, int httpStatusCode, string apiResponse, DateTime? deprecationDate = null)
            : base(message ?? throw new ArgumentNullException(nameof(message)))
        {
            if (string.IsNullOrWhiteSpace(message))
                throw new ArgumentException("Message cannot be empty or whitespace", nameof(message));
            if (string.IsNullOrWhiteSpace(endpointUrl))
                throw new ArgumentException("Endpoint URL cannot be null or empty", nameof(endpointUrl));

            EndpointUrl = endpointUrl;
            HttpStatusCode = httpStatusCode;
            ApiResponse = apiResponse ?? string.Empty;
            DeprecationDate = deprecationDate;
        }

        /// <summary>
        /// Initializes a new instance of the GenesysApiDeprecatedException class with an inner exception
        /// </summary>
        /// <param name="message">The error message that explains the reason for the exception</param>
        /// <param name="endpointUrl">The URL of the deprecated API endpoint</param>
        /// <param name="httpStatusCode">The HTTP status code returned</param>
        /// <param name="apiResponse">The raw API response content</param>
        /// <param name="innerException">The exception that is the cause of the current exception</param>
        /// <param name="deprecationDate">The date when the API was deprecated (optional)</param>
        /// <exception cref="ArgumentNullException">Thrown when message or endpointUrl is null</exception>
        /// <exception cref="ArgumentException">Thrown when message or endpointUrl is empty</exception>
        public GenesysApiDeprecatedException(string message, string endpointUrl, int httpStatusCode, string apiResponse, Exception innerException, DateTime? deprecationDate = null)
            : base(message ?? throw new ArgumentNullException(nameof(message)), innerException)
        {
            if (string.IsNullOrWhiteSpace(message))
                throw new ArgumentException("Message cannot be empty or whitespace", nameof(message));
            if (string.IsNullOrWhiteSpace(endpointUrl))
                throw new ArgumentException("Endpoint URL cannot be null or empty", nameof(endpointUrl));

            EndpointUrl = endpointUrl;
            HttpStatusCode = httpStatusCode;
            ApiResponse = apiResponse ?? string.Empty;
            DeprecationDate = deprecationDate;
        }

        /// <summary>
        /// Initializes a new instance of the GenesysApiDeprecatedException class with serialized data
        /// </summary>
        /// <param name="info">The SerializationInfo that holds the serialized object data</param>
        /// <param name="context">The StreamingContext that contains contextual information</param>
        protected GenesysApiDeprecatedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
            EndpointUrl = info.GetString(nameof(EndpointUrl)) ?? string.Empty;
            HttpStatusCode = info.GetInt32(nameof(HttpStatusCode));
            ApiResponse = info.GetString(nameof(ApiResponse)) ?? string.Empty;

            // Handle nullable DateTime for backward compatibility
            var deprecationDateString = info.GetString(nameof(DeprecationDate));
            DeprecationDate = string.IsNullOrEmpty(deprecationDateString) ? null : DateTime.Parse(deprecationDateString, System.Globalization.CultureInfo.InvariantCulture);
        }

        /// <summary>
        /// Sets the SerializationInfo with information about the exception
        /// </summary>
        /// <param name="info">The SerializationInfo that holds the serialized object data</param>
        /// <param name="context">The StreamingContext that contains contextual information</param>
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            base.GetObjectData(info, context);

            info.AddValue(nameof(EndpointUrl), EndpointUrl);
            info.AddValue(nameof(HttpStatusCode), HttpStatusCode);
            info.AddValue(nameof(ApiResponse), ApiResponse);
            info.AddValue(nameof(DeprecationDate), DeprecationDate?.ToString());
        }

        /// <summary>
        /// Creates a string representation of the exception with structured information
        /// </summary>
        /// <returns>A formatted string containing exception details</returns>
        public override string ToString()
        {
            var result = base.ToString();
            result += $"\nEndpoint URL: {EndpointUrl}";
            result += $"\nHTTP Status Code: {HttpStatusCode}";
            if (DeprecationDate.HasValue)
                result += $"\nDeprecation Date: {DeprecationDate.Value:yyyy-MM-dd}";
            if (!string.IsNullOrEmpty(ApiResponse))
                result += $"\nAPI Response: {(ApiResponse.Length > 200 ? ApiResponse.Substring(0, 200) + "..." : ApiResponse)}";

            return result;
        }
    }
}
