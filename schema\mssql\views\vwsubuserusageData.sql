-- Only create the view if the underlying deprecated table exists
-- Since subuserusageData is deprecated, we only create the view for existing installations
IF dbo.csg_table_exists('subuserusageData') = 1
BEGIN
    EXEC('CREATE OR ALTER VIEW [vwsubuserusageData] AS
    SELECT
        keyid,
        date,
        userlogin,
        licensename,
        secs,
        hoursstr,
        updated,
        deprecated_status,
        deprecated_date
    FROM
        subuserusageData');
    PRINT 'vwsubuserusageData view created - table exists';
END
ELSE
BEGIN
    PRINT 'vwsubuserusageData view not created - underlying table subuserusageData does not exist (deprecated)';
END
