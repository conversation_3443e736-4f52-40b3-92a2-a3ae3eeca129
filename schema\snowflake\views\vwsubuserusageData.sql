-- Only create the view if the underlying deprecated table exists
-- Since subuserusagedata is deprecated, we only create the view for existing installations

-- Create a procedure to handle conditional view creation
CREATE OR REPLACE PROCEDURE create_vwsubuserusagedata_if_table_exists()
  RETURNS STRING
  LANGUAGE JAVASCRIPT
  EXECUTE AS CALLER
AS
$$
  try {
    // Check if table exists
    var checkTableQuery = `SELECT COUNT(*) as table_count
                          FROM information_schema.tables
                          WHERE table_name = 'SUBUSERUSAGEDATA'`;
    var resultSet = snowflake.execute({sqlText: checkTableQuery});
    resultSet.next();
    var tableExists = resultSet.getColumnValue(1) > 0;

    if (tableExists) {
      // Table exists, create the view
      var createViewQuery = `CREATE OR REPLACE VIEW vwsubuserusagedata AS
      SELECT
          keyid,
          date,
          userlogin,
          licensename,
          secs,
          hoursstr,
          updated,
          deprecated_status,
          deprecated_date
      FROM
          subuserusagedata`;

      snowflake.execute({sqlText: createViewQuery});
      return 'vwsubuserusagedata view created - table exists';
    } else {
      // Table doesn't exist and is deprecated, so don't create the view
      return 'vwsubuserusagedata view not created - underlying table subuserusagedata does not exist (deprecated)';
    }
  } catch (e) {
    return 'Error creating vwsubuserusagedata view: ' + e.message;
  }
$$;

-- Execute the procedure
CALL create_vwsubuserusagedata_if_table_exists();