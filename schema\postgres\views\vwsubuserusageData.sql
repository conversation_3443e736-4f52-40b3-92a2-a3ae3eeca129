-- Only create the view if the underlying deprecated table exists
-- Since subuserusagedata is deprecated, we only create the view for existing installations
DO $$
BEGIN
    -- Check if table exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'subuserusagedata') THEN
        -- Create the view since the table exists
        EXECUTE 'CREATE OR REPLACE VIEW vwsubuserusagedata AS
        SELECT
            keyid,
            date,
            userlogin,
            licensename,
            secs,
            hoursstr,
            updated,
            deprecated_status,
            deprecated_date
        FROM
            subuserusagedata';

        -- Add comments
        COMMENT ON COLUMN vwsubuserusagedata.keyid IS 'Primary Key';
        COMMENT ON COLUMN vwsubuserusagedata.date IS 'Date';
        COMMENT ON COLUMN vwsubuserusagedata.userlogin IS 'User GUID';
        COMMENT ON COLUMN vwsubuserusagedata.licensename IS 'License Used';
        COMMENT ON COLUMN vwsubuserusagedata.secs IS 'Time in Secs';
        COMMENT ON COLUMN vwsubuserusagedata.hoursstr IS 'Hours string representation';
        COMMENT ON COLUMN vwsubuserusagedata.updated IS 'Date Row Updated (UTC)';
        COMMENT ON COLUMN vwsubuserusagedata.deprecated_status IS 'Deprecation status of the table (active/deprecated)';
        COMMENT ON COLUMN vwsubuserusagedata.deprecated_date IS 'Date when the underlying API was deprecated (2025-03-10)';
        COMMENT ON VIEW vwsubuserusagedata IS 'DEPRECATED: View containing subscription user usage data - No longer updated due to Genesys Cloud API deprecation (March 10, 2025). Historical data preserved for reference.';

        RAISE NOTICE 'vwsubuserusagedata view created - table exists';
    ELSE
        RAISE NOTICE 'vwsubuserusagedata view not created - underlying table subuserusagedata does not exist (deprecated)';
    END IF;
END $$;

