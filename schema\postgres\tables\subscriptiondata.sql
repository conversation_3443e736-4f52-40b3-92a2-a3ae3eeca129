-- Conditional drop of subscriptiondata table if it exists and is empty
-- This table was used for legacy subscription data and is no longer needed
-- Only drop if the table exists and contains no records to preserve any existing data

DO $$
DECLARE
    record_count INTEGER;
BEGIN
    -- Check if table exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'subscriptiondata') THEN
        -- Count records in the table
        SELECT COUNT(*) INTO record_count FROM subscriptiondata;

        IF record_count = 0 THEN
            DROP TABLE subscriptiondata;
            RAISE NOTICE 'Dropped empty subscriptiondata table';
        ELSE
            RAISE NOTICE 'Preserved subscriptiondata table - contains % records', record_count;
        END IF;
    END IF;
END $$;