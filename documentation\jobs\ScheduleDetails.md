# ScheduleDetails Job Documentation

## Overview

The **ScheduleDetails** job synchronizes detailed schedule information from Genesys Cloud Workforce Management (WFM). This job provides granular schedule data including activity details, time segments, and schedule variations that complement the broader schedule data from the WFMSchedule job.

## Data Flow Architecture

:::mermaid
graph TB
    subgraph "Genesys Cloud WFM APIs"
        AUTH[OAuth Authentication<br/>/api/v2/oauth/token]
        MU[Management Units<br/>/api/v2/workforcemanagement/managementunits]
        SCHED[Schedule Details<br/>/api/v2/workforcemanagement/schedules]
        ACTIVITIES[Activity Codes<br/>/api/v2/workforcemanagement/activitycodes]
    end
    
    subgraph "ScheduleDetails Job Process"
        SD[GCUpdateScheduleDetails<br/>Schedule Details Controller]
        FETCH[Data Fetcher<br/>Detailed Schedule Retrieval]
        PROC[Details Processor<br/>Activity Breakdown]
        SEGMENT[Segment Analyzer<br/>Time Segment Processing]
        VALIDATE[Data Validator<br/>Schedule Validation]
    end
    
    subgraph "Database Tables"
        SDD[(scheduledetails<br/>Detailed Schedule Records)]
        SAD[(scheduleactivitydata<br/>Activity Breakdown)]
        SSD[(schedulesegmentdata<br/>Time Segments)]
        SVD[(schedulevariationdata<br/>Schedule Variations)]
    end
    
    subgraph "Schedule Features"
        ACTIVITIES_PROC[Activity Processing<br/>Activity Code Mapping]
        SEGMENTS[Time Segments<br/>Schedule Breakdown]
        VARIATIONS[Variations<br/>Schedule Changes]
        PATTERNS[Pattern Analysis<br/>Schedule Patterns]
    end
    
    %% Main flow
    SD --> AUTH
    AUTH --> FETCH
    FETCH --> MU
    FETCH --> SCHED
    FETCH --> ACTIVITIES
    SCHED --> PROC
    PROC --> SEGMENT
    SEGMENT --> VALIDATE
    
    %% Feature processing
    PROC --> ACTIVITIES_PROC
    PROC --> SEGMENTS
    PROC --> VARIATIONS
    PROC --> PATTERNS
    
    %% Database storage
    VALIDATE --> SDD
    ACTIVITIES_PROC --> SAD
    SEGMENTS --> SSD
    VARIATIONS --> SVD
    
    %% Styling
    classDef api fill:#e1f5fe
    classDef process fill:#e8f5e8
    classDef database fill:#fff3e0
    classDef feature fill:#f3e5f5
    
    class AUTH,MU,SCHED,ACTIVITIES api
    class SD,FETCH,PROC,SEGMENT,VALIDATE process
    class SDD,SAD,SSD,SVD database
    class ACTIVITIES_PROC,SEGMENTS,VARIATIONS,PATTERNS feature
:::

## Configuration

### Basic Configuration
```json
{
  "Job": "ScheduleDetails",
  "Preferences": {
    "MaxSyncSpan": "14.00:00",
    "IncludeActivityBreakdown": true,
    "ProcessTimeSegments": true,
    "TrackScheduleVariations": true
  }
}
```

### ScheduleDetails-Specific Options

#### Detail Level Configuration
- **IncludeActivityBreakdown**: Include detailed activity code breakdown
- **ProcessTimeSegments**: Process individual time segments within schedules
- **TrackScheduleVariations**: Track schedule changes and variations
- **IncludeBreakDetails**: Include break and meal period details

#### Time Range Settings
- **MaxSyncSpan**: Maximum time range for detailed schedule sync
- **LookAheadDays**: Number of days to look ahead for future schedules
- **LookBackDays**: Number of days to look back for historical schedules

## Database Schema

### Primary Tables

#### scheduledetails
- **Purpose**: Detailed schedule records with granular information
- **Key Fields**: userid, date, starttime, endtime, activitycode, duration
- **Details**: Activity-level schedule breakdown, time segments
- **Indexes**: userid, date, starttime, activitycode

#### scheduleactivitydata
- **Purpose**: Activity code breakdown within schedules
- **Key Fields**: userid, date, activitycode, starttime, duration, activitytype
- **Features**: Activity categorization, time allocation, productivity tracking
- **Indexes**: userid, date, activitycode, activitytype

#### schedulesegmentdata
- **Purpose**: Individual time segments within schedules
- **Key Fields**: userid, date, segmentid, starttime, endtime, segmenttype
- **Segments**: Work periods, breaks, meetings, training sessions
- **Indexes**: userid, date, segmentid, segmenttype

#### schedulevariationdata
- **Purpose**: Schedule changes and variations from published schedules
- **Key Fields**: userid, date, variationtype, originaltime, newtime, reason
- **Tracking**: Schedule modifications, reason codes, approval status
- **Indexes**: userid, date, variationtype, reason

## API Integration

### WFM Schedule Details APIs

#### Schedule Details API
- **Endpoint**: `/api/v2/workforcemanagement/schedules`
- **Method**: GET with detailed expansion
- **Purpose**: Retrieve detailed schedule information with activity breakdown
- **Expansion**: Include activities, segments, variations

#### Activity Codes API
- **Endpoint**: `/api/v2/workforcemanagement/activitycodes`
- **Purpose**: Get activity code definitions and categorizations
- **Mapping**: Map activity codes to schedule activities

#### Query Parameters
```json
{
  "startDate": "2024-01-01",
  "endDate": "2024-01-14",
  "managementUnitIds": ["unit-id"],
  "userIds": ["user-id"],
  "expand": ["activities", "segments", "variations"]
}
```

## Schedule Detail Features

### Activity Breakdown
- **Activity Mapping**: Map schedule activities to activity codes
- **Time Allocation**: Track time allocation by activity type
- **Productivity Analysis**: Analyze productive vs. non-productive time
- **Activity Patterns**: Identify common activity patterns

### Time Segment Processing
- **Segment Types**: Work periods, breaks, meals, meetings, training
- **Duration Tracking**: Track duration of each segment type
- **Overlap Detection**: Identify overlapping or conflicting segments
- **Gap Analysis**: Detect gaps in schedule coverage

### Schedule Variations
- **Change Tracking**: Track all schedule modifications
- **Reason Codes**: Categorize reasons for schedule changes
- **Approval Workflow**: Track approval status of schedule changes
- **Impact Analysis**: Analyze impact of schedule variations

### Pattern Analysis
- **Schedule Patterns**: Identify common schedule patterns
- **Shift Patterns**: Analyze shift rotation and patterns
- **Break Patterns**: Track break timing and duration patterns
- **Efficiency Metrics**: Calculate schedule efficiency metrics

## Dependencies

### Prerequisites
- **WFMSchedule**: Base schedule data should be available
- **FactData**: User and management unit reference data
- **Database Schema**: Schedule details tables must be installed
- **API Permissions**: WFM schedule read permissions

### Related Jobs
- **WFMSchedule**: Provides base schedule information
- **Adherence**: Uses detailed schedule data for adherence calculation
- **WFMAudit**: Tracks schedule changes and modifications
- **TimeOffReq**: Time-off requests affecting detailed schedules

## Performance Optimization

### ScheduleDetails-Specific Optimizations
- **Activity Caching**: Cache activity code definitions
- **Segment Batching**: Process time segments in batches
- **Incremental Processing**: Only process changed schedule details
- **Parallel Processing**: Process multiple users concurrently

### Data Processing
- **Efficient Segmentation**: Optimize time segment processing
- **Activity Mapping**: Efficient activity code mapping and validation
- **Variation Tracking**: Optimize schedule variation detection

## Monitoring and Troubleshooting

### Key Metrics
- **Schedule Details Processed**: Number of detailed schedule records
- **Activity Breakdown Coverage**: Percentage of schedules with activity details
- **Segment Processing**: Number of time segments processed
- **Variation Detection**: Number of schedule variations detected

### Common Issues

#### Missing Activity Details
- **Symptoms**: Schedules without activity breakdown
- **Causes**: API expansion parameters, activity code mapping issues
- **Solution**: Verify API expansion and activity code definitions

#### Segment Processing Errors
- **Symptoms**: Incomplete or incorrect time segments
- **Causes**: Segment overlap, timing conflicts, data validation issues
- **Solution**: Review segment processing logic and validation rules

#### Schedule Variation Tracking
- **Symptoms**: Schedule changes not detected or tracked
- **Causes**: Variation detection logic, timing issues, API response delays
- **Solution**: Validate variation detection and tracking mechanisms

### ScheduleDetails-Specific Logging
- **Processing Progress**: Log schedule details processing status
- **Activity Mapping**: Log activity code mapping results
- **Segment Analysis**: Log time segment processing and validation
- **Variation Detection**: Log detected schedule variations and changes

## Usage Examples

### Standard Schedule Details Sync
```bash
GenesysAdapter --job ScheduleDetails --config scheduledetails-production.json
```

### Extended Detail Processing
```json
{
  "Job": "ScheduleDetails",
  "Preferences": {
    "IncludeActivityBreakdown": true,
    "ProcessTimeSegments": true,
    "TrackScheduleVariations": true,
    "IncludeBreakDetails": true
  }
}
```

### Historical Schedule Analysis
```json
{
  "Job": "ScheduleDetails",
  "Preferences": {
    "MaxSyncSpan": "30.00:00",
    "LookBackDays": 90,
    "TrackScheduleVariations": true
  }
}
```

## Best Practices

### Schedule Detail Management
- **Regular Sync**: Run schedule details job regularly for current data
- **Activity Validation**: Validate activity code mappings and definitions
- **Segment Integrity**: Ensure time segment integrity and completeness

### Performance Optimization
- **Incremental Processing**: Focus on changed schedule details
- **Efficient Batching**: Use appropriate batch sizes for processing
- **Activity Caching**: Cache activity code definitions for efficiency

### Data Quality
- **Schedule Validation**: Validate detailed schedule data for consistency
- **Activity Mapping**: Ensure accurate activity code mapping
- **Variation Tracking**: Track and validate schedule variations

### Analytical Applications
- **Productivity Analysis**: Use activity breakdown for productivity analysis
- **Schedule Optimization**: Analyze schedule patterns for optimization opportunities
- **Compliance Monitoring**: Monitor schedule compliance with labor regulations
- **Resource Planning**: Use detailed schedule data for resource planning and optimization
