# License Users Job Documentation

## Overview

The License Users job synchronizes current license assignments and user data from Genesys Cloud for monitoring license utilization and user management. This job replaces the deprecated SubsUsers functionality and provides real-time license assignment tracking.

## Data Flow Diagram

:::mermaid
graph TB
    subgraph "Genesys Cloud APIs"
        AUTH[OAuth Authentication<br/>/api/v2/oauth/token]
        LICENSE[License Users API<br/>/api/v2/license/users]
        USERS[Users API<br/>/api/v2/users]
    end

    subgraph "License Users Process"
        LU[GCUpdateAdminData<br/>License Users Controller]
        FETCH[Data Fetcher<br/>License Assignment Retrieval]
        VALIDATE[Data Validator<br/>User & License Validation]
        DIFF[Diffing Engine<br/>Change Detection]
        BATCH[Batch Processor<br/>Database Updates]
    end

    subgraph "Database Operations"
        CACHE[Data Caching<br/>In-Memory Processing]
        TRANSFORM[Data Transformation<br/>Schema Mapping]
        PERSIST[Database Persistence<br/>Bulk Operations]
    end

    subgraph "Database Tables"
        LUD[(licenseuserdata<br/>License Assignments)]
        UD[(userdetails<br/>User Information)]
    end

    subgraph "Error Handling"
        DEPRECATION[Deprecation Detection<br/>GenesysApiDeprecatedException]
        RETRY[Retry Logic<br/>Exponential Backoff]
        LOGGING[Structured Logging<br/>Performance Metrics]
    end

    %% Main flow
    LU --> AUTH
    AUTH --> FETCH
    FETCH --> LICENSE
    FETCH --> USERS
    LICENSE --> VALIDATE
    USERS --> VALIDATE
    VALIDATE --> DIFF
    DIFF --> CACHE
    CACHE --> TRANSFORM
    TRANSFORM --> BATCH
    BATCH --> PERSIST
    PERSIST --> LUD

    %% Reference data
    UD -.-> VALIDATE

    %% Error handling
    FETCH --> DEPRECATION
    FETCH --> RETRY
    LU --> LOGGING

    %% Styling
    classDef api fill:#e1f5fe
    classDef process fill:#e8f5e8
    classDef database fill:#fff3e0
    classDef error fill:#ffebee

    class AUTH,LICENSE,USERS api
    class LU,FETCH,VALIDATE,DIFF,BATCH process
    class CACHE,TRANSFORM,PERSIST,LUD,UD database
    class DEPRECATION,RETRY,LOGGING error
:::

## Configuration

### Required Settings
- **GenesysApi**: OAuth credentials with license management permissions
- **Database**: Connection string for target database

### Optional Settings
- **DiffingEnabled**: Enable change detection (default: true)
- **BatchSize**: Records per database operation (default: 1000)
- **IncludeInactiveUsers**: Include disabled users (default: false)

### Example Configuration
```json
{
  "Job": "LicenseUsers",
  "GenesysApi": {
    "ClientId": "your-client-id",
    "ClientSecret": "your-client-secret",
    "Region": "us-east-1"
  },
  "Preferences": {
    "DiffingEnabled": true,
    "BatchSize": 1000
  }
}
```

## Database Tables

### licenseuserdata
- **Purpose**: Current license assignments per user
- **Schema**: Simplified to only include data available from the API
- **Fields**:
  - `keyid` (varchar(100)): Primary key (userid + licensename)
  - `userid` (varchar(50)): Genesys Cloud user ID
  - `licensename` (varchar(200)): Name of the assigned license
  - `updated` (datetime): Last update timestamp

### vwlicenseuserdata
- **Purpose**: Normalized view of license user data with complete user details
- **Description**: Joins licenseuserdata with vwuserdetail to provide comprehensive user information
- **Key Fields**:
  - All fields from `licenseuserdata`
  - `username` (varchar(200)): User display name
  - `email` (varchar(200)): User email address
  - `title` (varchar(200)): User job title
  - `department` (varchar(200)): User department
  - `employeeid` (varchar(50)): Employee ID
  - `datehire` (varchar(50)): Date of hire
  - `managerid` (varchar(50)): Manager's user ID
  - `managername` (varchar(200)): Manager's display name
  - `divisionid` (varchar(50)): Division ID
  - `divisionname` (varchar(200)): Division name
  - `homedivision` (boolean): Whether this is the user's home division
- **Usage**: Recommended for reporting and analytics requiring user context

### Indexes
- Primary key on `keyid`
- Index on `userid` for user-based queries
- Index on `licenseid` for license-based reporting
- Index on `updated` for incremental processing

## API Endpoints

### Primary API
- `/api/v2/license/users` - License assignment data
  - Returns current license assignments for all users
  - Response contains only: user ID, license names array, and selfUri
  - Does not include user details (name, email), assignment timestamps, expiry dates, or status details
  - Supports pagination for large organizations

### Supporting APIs
- `/api/v2/oauth/token` - Authentication
- `/api/v2/users` - User validation and details

## Dependencies

### Prerequisites
- **OAuth Permissions**: `license:readonly` or `license:all`
- **Database Schema**: licenseuserdata table must exist
- **Users Job**: Should run before or after for complete user data

### Related Jobs
- **Users**: Provides user details and validation
- **FactData**: May use license data for reporting
- **OAuthUsage**: Tracks API consumption

## Processing Logic

### Data Collection
1. Authenticate with Genesys Cloud
2. Retrieve all license assignments via paginated API calls
3. Validate user IDs against existing user data
4. Apply business rules for license assignment logic

### Change Detection
1. Compare new data with existing database records
2. Identify new assignments, removals, and modifications
3. Generate differential dataset for efficient updates
4. Preserve historical assignment data where configured

### Database Operations
1. Batch process changes for optimal performance
2. Use bulk insert/update operations
3. Maintain referential integrity with user data
4. Update sync timestamps for tracking

## Monitoring

### Key Metrics
- **Total Users Processed**: Count of users with license data
- **License Types**: Distinct license types in use
- **Assignment Changes**: New, modified, removed assignments
- **Processing Time**: Total job execution duration
- **API Calls**: Number of API requests made

### Performance Indicators
- **Records per Second**: Processing throughput
- **API Response Time**: Average API call duration
- **Database Write Time**: Bulk operation performance
- **Memory Usage**: Peak memory consumption during processing

### Alerts
- **API Deprecation**: Monitor for deprecation warnings
- **License Quota**: Track license utilization trends
- **Processing Failures**: Failed API calls or database operations

## Troubleshooting

### Common Issues

#### API Deprecation Errors
- **Symptoms**: GenesysApiDeprecatedException thrown
- **Causes**: Genesys Cloud API endpoint deprecated
- **Solutions**: Update to newer API version, check Genesys documentation

#### Missing License Data
- **Symptoms**: Empty or incomplete license assignments
- **Causes**: Insufficient permissions, API rate limiting
- **Solutions**: Verify OAuth scopes, check rate limit headers

#### Performance Issues
- **Symptoms**: Slow processing, timeouts
- **Causes**: Large user base, inefficient queries
- **Solutions**: Increase batch sizes, optimize database indexes

### Error Handling
- **Rate Limiting**: Automatic retry with exponential backoff
- **API Failures**: Detailed logging with error context
- **Data Validation**: Skip invalid records with warnings

## Migration from SubsUsers

### Key Differences
- **API Endpoint**: Uses `/api/v2/license/users` instead of deprecated billing API
- **Data Structure**: Simplified schema focused on current assignments
- **Real-time Data**: Current assignments vs historical usage
- **Performance**: More efficient API with better pagination

### Migration Steps
1. Run LicenseUsers job to populate new table
2. Update reports to use licenseuserdata instead of subuserusagedata
3. Verify data accuracy and completeness
4. Decommission SubsUsers job references

## Performance Optimization

### Best Practices
- Enable diffing to process only changed data
- Use appropriate batch sizes for your environment
- Monitor API rate limits and adjust accordingly
- Schedule during off-peak hours for large organizations

### Scaling Considerations
- Large organizations (>10,000 users) may need longer processing windows
- Consider memory usage for organizations with many license types
- Monitor database performance during bulk operations

## Examples

### Basic License Sync
```bash
GenesysAdapter --job LicenseUsers
```

### High-Volume Environment
```json
{
  "Job": "LicenseUsers",
  "Preferences": {
    "BatchSize": 5000,
    "DiffingEnabled": true
  }
}
```

### Include Inactive Users
```json
{
  "Job": "LicenseUsers",
  "Preferences": {
    "IncludeInactiveUsers": true
  }
}
```

### Monitoring Configuration
```json
{
  "Job": "LicenseUsers",
  "Logging": {
    "Level": "Information",
    "IncludePerformanceMetrics": true
  }
}
```
