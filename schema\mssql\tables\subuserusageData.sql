-- Only create the subuserusageData table if it doesn't exist and we're not in deprecation mode
-- Since this table is deprecated, we don't create it for new installations
IF dbo.csg_table_exists('subuserusageData') = 1
BEGIN
    -- Table exists, add deprecation columns for existing installations
    IF dbo.csg_column_exists('subuserusageData', 'deprecated_status') = 0
        ALTER TABLE subuserusageData ADD deprecated_status [nvarchar](50) DEFAULT 'deprecated';

    IF dbo.csg_column_exists('subuserusageData', 'deprecated_date') = 0
        ALTER TABLE subuserusageData ADD deprecated_date [datetime] DEFAULT '2025-03-10 00:00:00';

    PRINT 'subuserusageData table exists - added deprecation tracking columns';
END
ELSE
BEGIN
    -- Table doesn't exist and is deprecated, so don't create it
    PRINT 'subuserusageData table does not exist and is deprecated - skipping table creation';
END
