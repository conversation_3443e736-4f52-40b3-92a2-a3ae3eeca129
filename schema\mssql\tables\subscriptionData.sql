-- Conditional drop of subscriptionData table if it exists and is empty
-- This table was used for legacy subscription data and is no longer needed
-- Only drop if the table exists and contains no records to preserve any existing data

IF dbo.csg_table_exists('subscriptionData') = 1
BEGIN
    DECLARE @RecordCount INT;
    SELECT @RecordCount = COUNT(*) FROM [subscriptionData];

    IF @RecordCount = 0
    BEGIN
        DROP TABLE [subscriptionData];
        PRINT 'Dropped empty subscriptionData table';
    END
    ELSE
    BEGIN
        PRINT 'Preserved subscriptionData table - contains ' + CAST(@RecordCount AS VARCHAR(10)) + ' records';
    END
END
