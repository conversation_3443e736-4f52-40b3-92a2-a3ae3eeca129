# Information Job Documentation

## Overview

The **Information** job provides comprehensive system information about the Genesys Adapter installation, configuration, and operational status. This diagnostic job displays version information, job definitions, database schema status, API connectivity, and system health metrics without performing any data synchronization operations.

## Data Flow Architecture

:::mermaid
graph TB
    subgraph "Information Sources"
        VERSION[Version Information<br/>Assembly Metadata]
        CONFIG[Configuration<br/>Current Settings]
        DATABASE[Database Schema<br/>Installation Status]
        API[API Connectivity<br/>Genesys Cloud Status]
    end
    
    subgraph "Information Job Process"
        INFO[GCInformation<br/>Information Controller]
        COLLECT[Data Collector<br/>System Information Gathering]
        VALIDATE[Validator<br/>System Health Checks]
        FORMAT[Formatter<br/>Output Generation]
        DISPLAY[Display Engine<br/>Information Presentation]
    end
    
    subgraph "Information Categories"
        SYS[System Information<br/>Version, Platform, Runtime]
        JOBS[Job Definitions<br/>Available Jobs and Descriptions]
        DB[Database Status<br/>Schema Version, Table Status]
        CONN[Connectivity<br/>API Status, Permissions]
        HEALTH[Health Metrics<br/>System Performance]
    end
    
    subgraph "Output Formats"
        CONSOLE[Console Output<br/>Formatted Text Display]
        JSON[JSON Format<br/>Structured Data Output]
        LOG[Log Output<br/>Detailed Logging]
        REPORT[Report Format<br/>Comprehensive Report]
    end
    
    %% Main flow
    INFO --> COLLECT
    COLLECT --> VERSION
    COLLECT --> CONFIG
    COLLECT --> DATABASE
    COLLECT --> API
    
    %% Processing
    COLLECT --> VALIDATE
    VALIDATE --> FORMAT
    FORMAT --> DISPLAY
    
    %% Information categories
    VALIDATE --> SYS
    VALIDATE --> JOBS
    VALIDATE --> DB
    VALIDATE --> CONN
    VALIDATE --> HEALTH
    
    %% Output generation
    DISPLAY --> CONSOLE
    DISPLAY --> JSON
    DISPLAY --> LOG
    DISPLAY --> REPORT
    
    %% Styling
    classDef source fill:#e1f5fe
    classDef process fill:#e8f5e8
    classDef category fill:#fff3e0
    classDef output fill:#f3e5f5
    
    class VERSION,CONFIG,DATABASE,API source
    class INFO,COLLECT,VALIDATE,FORMAT,DISPLAY process
    class SYS,JOBS,DB,CONN,HEALTH category
    class CONSOLE,JSON,LOG,REPORT output
:::

## Configuration

### Basic Configuration
```json
{
  "Job": "Information",
  "OutputFormat": "Console",
  "IncludeHealthChecks": true,
  "IncludeJobDetails": true
}
```

### Information Options

#### Output Format Selection
- **Console**: Formatted text output to console (default)
- **JSON**: Structured JSON output for programmatic use
- **Log**: Detailed logging output with timestamps
- **Report**: Comprehensive report format

#### Information Scope
- **Basic**: Core system information only
- **Detailed**: Comprehensive system information
- **HealthCheck**: Include system health validation
- **JobDetails**: Include detailed job descriptions

## Information Categories

### System Information

#### Version Details
- **Adapter Version**: Current Genesys Adapter version
- **Assembly Information**: Build date, version, and metadata
- **Runtime Environment**: .NET version, platform, architecture
- **Operating System**: OS version and platform details

#### Configuration Status
- **Configuration File**: Current configuration file location and status
- **Database Connection**: Database type and connection status
- **API Configuration**: Genesys Cloud API configuration
- **Job Settings**: Current job configuration and preferences

### Job Information

#### Available Jobs
- **Job List**: Complete list of all available jobs
- **Job Descriptions**: Detailed description of each job's purpose
- **Job Dependencies**: Dependencies between jobs
- **Job Status**: Current status and last execution information

#### Job Categories
- **Administrative Jobs**: User, queue, and organizational data jobs
- **Interaction Jobs**: Conversation and communication data jobs
- **WFM Jobs**: Workforce management and scheduling jobs
- **Real-time Jobs**: Live data synchronization jobs
- **Fact Data Jobs**: Reference data synchronization jobs

### Database Information

#### Schema Status
- **Installation Status**: Database schema installation verification
- **Table Versions**: Version information for all database tables
- **Missing Tables**: Identification of missing or incomplete tables
- **Schema Integrity**: Validation of database schema completeness

#### Database Metrics
- **Connection Status**: Database connectivity verification
- **Performance Metrics**: Database response times and performance
- **Storage Information**: Database size and storage utilization
- **Index Status**: Database index status and optimization

### API Connectivity

#### Genesys Cloud Status
- **Authentication**: OAuth token status and validity
- **API Endpoints**: Connectivity to key Genesys Cloud APIs
- **Rate Limiting**: Current rate limit status and usage
- **Permissions**: API permission validation

#### Network Connectivity
- **DNS Resolution**: Genesys Cloud hostname resolution
- **SSL/TLS Status**: Certificate validation and security status
- **Proxy Configuration**: Network proxy settings and status
- **Firewall Status**: Network connectivity validation

### Health Metrics

#### System Performance
- **Memory Usage**: Current memory utilization
- **CPU Usage**: Processor utilization metrics
- **Disk Space**: Available disk space and usage
- **Network Performance**: Network latency and throughput

#### Application Health
- **Service Status**: Application service status
- **Error Rates**: Recent error rates and patterns
- **Performance Trends**: Historical performance trends
- **Resource Utilization**: Resource usage patterns

## Dependencies

### No Operational Dependencies
- **Independent Operation**: Information job requires no other jobs
- **Read-Only**: Does not modify any data or configuration
- **Safe Execution**: Can be run at any time without impact

### System Requirements
- **Database Access**: Read access to database for schema validation
- **API Access**: Optional API access for connectivity testing
- **File System**: Access to configuration files and logs

## Output Examples

### Console Output Format
```
Genesys Adapter Information
===========================

System Information:
- Version: 2.1.0
- Build Date: 2024-01-15
- Platform: .NET 6.0 on Windows 10
- Database: PostgreSQL 14.2

Available Jobs:
- Interaction: Synchronize conversation data
- Chat: Synchronize chat conversations
- VoiceAnalysis: Process voice analytics
- Realtime: Real-time data synchronization
- FactData: Reference data synchronization
[... additional jobs ...]

Database Status:
- Schema Version: 2.1.0
- Tables Installed: 45/45
- Views Installed: 12/12
- Functions Installed: 8/8

API Connectivity:
- Authentication: ✓ Valid
- Conversations API: ✓ Connected
- Users API: ✓ Connected
- Rate Limit Status: 150/300 requests used
```

### JSON Output Format
```json
{
  "systemInfo": {
    "version": "2.1.0",
    "buildDate": "2024-01-15",
    "platform": ".NET 6.0",
    "operatingSystem": "Windows 10",
    "database": "PostgreSQL 14.2"
  },
  "jobs": [
    {
      "name": "Interaction",
      "description": "Synchronize conversation data",
      "category": "Interaction",
      "dependencies": ["FactData"]
    }
  ],
  "databaseStatus": {
    "schemaVersion": "2.1.0",
    "tablesInstalled": 45,
    "tablesExpected": 45,
    "viewsInstalled": 12,
    "functionsInstalled": 8
  },
  "apiConnectivity": {
    "authentication": "valid",
    "endpoints": {
      "conversations": "connected",
      "users": "connected"
    },
    "rateLimitUsage": "150/300"
  }
}
```

## Usage Examples

### Basic Information Display
```bash
GenesysAdapter --job Information
```

### Detailed Information with Health Checks
```bash
GenesysAdapter --job Information --detailed true --healthcheck true
```

### JSON Output for Automation
```bash
GenesysAdapter --job Information --format json > system-info.json
```

### Configuration-Specific Information
```bash
GenesysAdapter --job Information --config production.json
```

## Monitoring and Diagnostics

### System Health Validation
- **Database Connectivity**: Verify database connection and schema
- **API Authentication**: Validate Genesys Cloud API access
- **Configuration Validation**: Check configuration file integrity
- **Resource Availability**: Verify system resource availability

### Troubleshooting Support
- **Error Diagnosis**: Identify common configuration and connectivity issues
- **Version Verification**: Confirm adapter and dependency versions
- **Permission Validation**: Verify required permissions and access
- **Network Diagnostics**: Test network connectivity and performance

### Performance Monitoring
- **Response Times**: Measure database and API response times
- **Resource Usage**: Monitor memory, CPU, and disk utilization
- **Throughput Metrics**: Measure system throughput capabilities
- **Bottleneck Identification**: Identify performance bottlenecks

## Best Practices

### Regular Health Checks
- **Scheduled Execution**: Run Information job regularly for health monitoring
- **Automated Monitoring**: Integrate with monitoring systems for automated checks
- **Baseline Establishment**: Establish performance baselines for comparison
- **Trend Analysis**: Track system health trends over time

### Troubleshooting Workflow
- **First Diagnostic Step**: Run Information job as first troubleshooting step
- **Configuration Validation**: Verify configuration before investigating issues
- **Connectivity Testing**: Test API and database connectivity before job execution
- **Version Verification**: Confirm version compatibility for all components

### Documentation and Reporting
- **System Documentation**: Use Information job output for system documentation
- **Change Management**: Document system changes and version updates
- **Compliance Reporting**: Generate compliance reports for audit purposes
- **Capacity Planning**: Use metrics for capacity planning and scaling decisions

### Integration with Operations
- **Monitoring Integration**: Integrate with existing monitoring and alerting systems
- **Automation Scripts**: Use JSON output in automation and orchestration scripts
- **Health Dashboards**: Include Information job metrics in operational dashboards
- **Incident Response**: Include Information job in incident response procedures
