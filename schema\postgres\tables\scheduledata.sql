CREATE TABLE IF NOT EXISTS scheduledata (
    keyid varchar(100) NOT NULL,
    userid varchar(50),
    shiftstartdate timestamp without time zone,
    shiftlengthtime integer,
    activitystartdate timestamp without time zone,
    activitystartdateltc timestamp without time zone,
    activitylengthtime integer,
    activitydescription varchar(200),
    activitycodeid varchar(50),
    activitypaid bit(1),
    shiftmanuallyeditted bit(1),
    buid varchar(50),
    shiftid varchar(50),
    scheduleid varchar(50),
    updated timestamp without time zone,
    CONSTRAINT scheduledata_pkey PRIMARY KEY (keyid)
) TABLESPACE pg_default;

COMMENT ON TABLE scheduledata IS 'Workforce management schedule data for agents including shifts and activities';
COMMENT ON COLUMN scheduledata.keyid IS 'Primary Key';
COMMENT ON COLUMN scheduledata.userid IS 'User GUID';
COMMENT ON COLUMN scheduledata.shiftstartdate IS 'Start date and time of the shift';
COMMENT ON COLUMN scheduledata.shiftlengthtime IS 'Length of shift in seconds';
COMMENT ON COLUMN scheduledata.activitystartdate IS 'Start date and time of the activity';
COMMENT ON COLUMN scheduledata.activitystartdateltc IS 'Start date and time of the activity in local time';
COMMENT ON COLUMN scheduledata.activitylengthtime IS 'Length of activity in seconds';
COMMENT ON COLUMN scheduledata.activitydescription IS 'Description of the activity';
COMMENT ON COLUMN scheduledata.activitycodeid IS 'Activity code identifier';
COMMENT ON COLUMN scheduledata.activitypaid IS 'Flag indicating if the activity is paid';
COMMENT ON COLUMN scheduledata.shiftmanuallyeditted IS 'Flag indicating if the shift was manually edited';
COMMENT ON COLUMN scheduledata.buid IS 'Business unit identifier';
COMMENT ON COLUMN scheduledata.shiftid IS 'Shift identifier';
COMMENT ON COLUMN scheduledata.scheduleid IS 'Schedule identifier';
COMMENT ON COLUMN scheduledata.updated IS 'Timestamp of last update';
