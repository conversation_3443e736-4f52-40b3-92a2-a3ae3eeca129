# Adherence Job Documentation

## Overview

The **Adherence** job synchronizes agent schedule adherence data from Genesys Cloud Workforce Management (WFM). This job tracks how well agents follow their published schedules, providing critical metrics for workforce optimization, performance management, and operational efficiency analysis.

## Data Flow Architecture

:::mermaid
graph TB
    subgraph "Genesys Cloud WFM APIs"
        AUTH[OAuth Authentication<br/>/api/v2/oauth/token]
        MU[Management Units<br/>/api/v2/workforcemanagement/managementunits]
        ADHER[Adherence API<br/>/api/v2/workforcemanagement/adherence]
        USERS[Users API<br/>/api/v2/users]
    end
    
    subgraph "Adherence Job Process"
        ADH[GCUpdateAdherenceData<br/>Adherence Controller]
        FETCH[Data Fetcher<br/>Adherence Retrieval]
        CALC[Adherence Calculator<br/>Metrics Computation]
        VALIDATE[Data Validator<br/>Schedule Validation]
        BATCH[Batch Processor<br/>Database Updates]
    end
    
    subgraph "Database Tables"
        AD[(adherencedata<br/>Adherence Records)]
        ASD[(adherencesummarydata<br/>Daily Summaries)]
        AED[(adherenceexceptiondata<br/>Exception Tracking)]
        TD[(tabledefinitions<br/>Sync Tracking)]
    end
    
    subgraph "Adherence Metrics"
        SCHED[Schedule Adherence<br/>On-time Performance]
        CONFORM[Conformance<br/>Activity Compliance]
        EXCEPT[Exceptions<br/>Unplanned Activities]
        VARIANCE[Variance Analysis<br/>Schedule Deviations]
    end
    
    %% Main flow
    ADH --> AUTH
    AUTH --> FETCH
    FETCH --> MU
    FETCH --> ADHER
    FETCH --> USERS
    ADHER --> CALC
    CALC --> VALIDATE
    VALIDATE --> BATCH
    
    %% Metrics calculation
    CALC --> SCHED
    CALC --> CONFORM
    CALC --> EXCEPT
    CALC --> VARIANCE
    
    %% Database storage
    BATCH --> AD
    BATCH --> ASD
    BATCH --> AED
    BATCH --> TD
    
    %% Styling
    classDef api fill:#e1f5fe
    classDef process fill:#e8f5e8
    classDef database fill:#fff3e0
    classDef metrics fill:#f3e5f5
    
    class AUTH,MU,ADHER,USERS api
    class ADH,FETCH,CALC,VALIDATE,BATCH process
    class AD,ASD,AED,TD database
    class SCHED,CONFORM,EXCEPT,VARIANCE metrics
:::

## Configuration

### Basic Configuration
```json
{
  "Job": "Adherence",
  "Preferences": {
    "MaxSyncSpan": "7.00:00",
    "LookBackSpan": "14.00:00"
  }
}
```

### Adherence-Specific Options

#### Time Range Configuration
- **MaxSyncSpan**: Maximum time range for single sync operation (default: 7 days)
- **LookBackSpan**: How far back to look for adherence data (default: 14 days)
- **Granularity**: Adherence measurement intervals (15-minute intervals)

#### Management Unit Selection
```json
{
  "Preferences": {
    "ManagementUnits": [
      "management-unit-id-1",
      "management-unit-id-2"
    ],
    "IncludeAllUnits": false
  }
}
```

## Database Schema

### Primary Tables

#### adherencedata
- **Purpose**: Individual adherence records for agents
- **Key Fields**: userid, date, scheduledtime, actualtime, adherencestatus
- **Metrics**: Schedule adherence percentage, conformance status
- **Indexes**: userid, date, scheduledtime

#### adherencesummarydata
- **Purpose**: Daily adherence summaries by agent and management unit
- **Key Fields**: userid, date, managementunitid, adherencepercentage
- **Aggregations**: Daily totals, averages, exception counts
- **Indexes**: userid, date, managementunitid

#### adherenceexceptiondata
- **Purpose**: Adherence exceptions and unplanned activities
- **Key Fields**: userid, date, exceptiontype, starttime, duration
- **Categories**: Late arrival, early departure, unscheduled break
- **Indexes**: userid, date, exceptiontype

## API Integration

### WFM Adherence APIs

#### Management Units API
- **Endpoint**: `/api/v2/workforcemanagement/managementunits`
- **Purpose**: Retrieve management unit configuration
- **Usage**: Get list of management units for adherence tracking

#### Adherence API
- **Endpoint**: `/api/v2/workforcemanagement/adherence`
- **Method**: POST
- **Purpose**: Retrieve adherence data for specified users and date ranges
- **Rate Limits**: WFM-specific rate limiting applies

#### Query Parameters
```json
{
  "startDate": "2024-01-01",
  "endDate": "2024-01-07",
  "managementUnitIds": ["unit-id-1", "unit-id-2"],
  "userIds": ["user-id-1", "user-id-2"]
}
```

## Adherence Metrics

### Core Adherence Measurements

#### Schedule Adherence
- **Definition**: Percentage of time agent follows published schedule
- **Calculation**: (Scheduled Time - Variance) / Scheduled Time * 100
- **Threshold**: Typically 90-95% target adherence

#### Conformance
- **Definition**: Agent performing correct activity at scheduled time
- **Types**: In conformance, out of conformance, exception
- **Tracking**: Activity-level compliance monitoring

#### Exception Tracking
- **Late Start**: Agent starts work later than scheduled
- **Early End**: Agent ends work earlier than scheduled
- **Unscheduled Break**: Breaks not included in published schedule
- **Wrong Activity**: Performing different activity than scheduled

### Performance Indicators
- **Daily Adherence**: Overall adherence for each day
- **Weekly Trends**: Adherence patterns over time
- **Exception Frequency**: Number of exceptions per agent
- **Variance Analysis**: Time variance from scheduled activities

## Dependencies

### Prerequisites
- **WFMSchedule**: Published schedules must be available
- **FactData**: User and management unit reference data
- **Database Schema**: Adherence tables must be installed
- **API Permissions**: WFM adherence read permissions

### Related Jobs
- **WFMSchedule**: Provides baseline schedule data for adherence calculation
- **ScheduleDetails**: Detailed schedule information for variance analysis
- **WFMAudit**: Audit trail for schedule and adherence changes
- **TimeOffReq**: Time-off requests affecting adherence calculations

## Performance Optimization

### Adherence-Specific Optimizations
- **Batch Processing**: Process adherence data in management unit batches
- **Date Range Optimization**: Limit date ranges to prevent API timeouts
- **User Filtering**: Process only active agents with published schedules
- **Incremental Updates**: Only sync new or modified adherence data

### Calculation Efficiency
- **Metric Caching**: Cache calculated adherence metrics
- **Parallel Processing**: Process multiple management units concurrently
- **Database Indexing**: Optimize indexes for adherence queries

## Monitoring and Troubleshooting

### Key Metrics
- **Adherence Records Processed**: Number of adherence records synchronized
- **Calculation Accuracy**: Validation of adherence calculations
- **Exception Detection**: Identification of adherence exceptions
- **Data Completeness**: Coverage of all scheduled agents

### Common Issues

#### Missing Adherence Data
- **Symptoms**: Gaps in adherence records for specific agents or dates
- **Causes**: Schedule not published, agent not assigned to management unit
- **Solution**: Verify schedule publication and management unit assignments

#### Incorrect Adherence Calculations
- **Symptoms**: Adherence percentages don't match Genesys Cloud UI
- **Causes**: Time zone issues, calculation logic differences
- **Solution**: Validate time zone handling and calculation methodology

#### API Rate Limiting
- **Symptoms**: Adherence API calls failing with rate limit errors
- **Causes**: Too many concurrent requests, large date ranges
- **Solution**: Implement proper rate limiting and batch size optimization

### Adherence-Specific Logging
- **Processing Status**: Log adherence processing progress by management unit
- **Calculation Results**: Log adherence metrics and exception detection
- **Data Validation**: Log validation results for adherence calculations

## Usage Examples

### Standard Adherence Sync
```bash
GenesysAdapter --job Adherence --config adherence-production.json
```

### Historical Adherence Analysis
```json
{
  "Job": "Adherence",
  "Preferences": {
    "MaxSyncSpan": "30.00:00",
    "LookBackSpan": "90.00:00"
  }
}
```

### Management Unit Specific
```json
{
  "Job": "Adherence",
  "Preferences": {
    "ManagementUnits": ["call-center-mu", "support-team-mu"],
    "MaxSyncSpan": "7.00:00"
  }
}
```

## Best Practices

### Adherence Monitoring
- **Regular Sync**: Run adherence job multiple times daily for current data
- **Historical Analysis**: Maintain historical adherence data for trend analysis
- **Exception Tracking**: Monitor adherence exceptions for coaching opportunities

### Data Quality
- **Schedule Validation**: Ensure published schedules exist before adherence calculation
- **Time Zone Consistency**: Maintain consistent time zone handling
- **Metric Validation**: Regularly validate adherence calculations against source data

### Performance Management
- **Threshold Monitoring**: Set up alerts for adherence below target thresholds
- **Trend Analysis**: Track adherence trends over time for performance insights
- **Exception Analysis**: Analyze exception patterns for operational improvements

### Reporting and Analytics
- **Real-time Dashboards**: Create dashboards for current adherence status
- **Historical Reports**: Generate reports for adherence trend analysis
- **Exception Reports**: Detailed reports on adherence exceptions and patterns
