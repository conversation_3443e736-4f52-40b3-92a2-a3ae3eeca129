# Genesys Adapter Jobs Documentation

This directory contains detailed documentation for each job type supported by the Genesys Adapter. Each job has its own documentation file with comprehensive information about its purpose, data flow, configuration, and troubleshooting.

## Job Categories

### Administrative Jobs ✅
- [QueueMembership](QueueMembership.md) - Queue membership management ✅
- [LicenseUsers](LicenseUsers.md) - License assignment tracking ✅
- [OAuthUsage](OAuthUsage.md) - API usage monitoring ✅
- UserQueueMapping - User-queue relationship tracking ❌ *Missing Documentation*
- UserQueueAudit - Queue assignment audit trail ❌ *Missing Documentation*

### Interaction Data Jobs ✅
- [Interaction](Interaction.md) - Detailed conversation data ✅
- [Chat](Chat.md) - Chat conversation analytics ✅
- [Message](Message.md) - Message conversation analytics ✅
- [VoiceAnalysis](VoiceAnalysis.md) - Voice analytics and sentiment ✅
- [Evaluation](Evaluation.md) - Quality evaluation data ✅
- EvaluationCatchup - Pending evaluation processing ❌ *Missing Documentation*

### Workforce Management Jobs 🔄
- [WFMSchedule](WFMSchedule.md) - Agent schedule data ✅
- [ScheduleDetails](ScheduleDetails.md) - Detailed schedule information ✅
- [Adherence](Adherence.md) - Schedule adherence tracking ✅
- WFMAudit - WFM audit trail ❌ *Missing Documentation*
- TimeOffReq - Time-off request management ❌ *Missing Documentation*
- Shrinkage - Shrinkage calculation and tracking ❌ *Missing Documentation*

### Real-time Data Jobs 🔄
- [Realtime](Realtime.md) - Live queue and user metrics ✅
- PresenceDetail - Detailed presence tracking ❌ *Missing Documentation*
- InteractionPresence - Interaction presence correlation ❌ *Missing Documentation*

### Reference Data Jobs 🔄
- [FactData](FactData.md) - Reference data synchronization ✅
- Knowledge - Knowledge management data ❌ *Missing Documentation*
- KnowledgeBaseDetails - Knowledge base configuration ❌ *Missing Documentation*
- Learning - Learning module data ❌ *Missing Documentation*
- LearningDataDetails - Learning module configuration ❌ *Missing Documentation*

### Outbound Dialing Jobs ❌
- ODContactLists - Contact list management ❌ *Missing Documentation*
- ODDetails - Outbound dialing configuration ❌ *Missing Documentation*

### Forecasting Jobs ❌
- HeadCountForecast - Staffing predictions ❌ *Missing Documentation*
- OfferedForecast - Queue forecast data ❌ *Missing Documentation*
- HoursBlockData - Hours block forecasting ❌ *Missing Documentation*

### Survey and Analytics Jobs ❌
- Survey - Survey data synchronization ❌ *Missing Documentation*
- SysConvUsage - System conversation usage ❌ *Missing Documentation*

### Utility Jobs ✅
- Aggregation - Data aggregation processes ❌ *Missing Documentation*
- [Install](Install.md) - Database schema installation ✅
- [Information](Information.md) - System information display ✅
- Subscription - Subscription management ❌ *Missing Documentation*

### Deprecated Jobs ⚠️
- [SubsUsers](SubsUsers.md) - ⚠️ **DEPRECATED** - Legacy subscription data ✅

## Documentation Structure

Each job documentation file includes:

1. **Overview** - Purpose and description
2. **Data Flow Diagram** - Mermaid chart showing the complete data flow
3. **Configuration** - Required settings and options
4. **Database Tables** - Tables populated by the job
5. **API Endpoints** - Genesys Cloud APIs used
6. **Dependencies** - Prerequisites and related jobs
7. **Monitoring** - Key metrics and logging
8. **Troubleshooting** - Common issues and solutions
9. **Examples** - Configuration and usage examples

## Quick Reference - Documented Jobs

| Job | Type | Frequency | Primary Tables | Key APIs | Status |
|-----|------|-----------|----------------|----------|--------|
| [Adherence](Adherence.md) | WFM | Daily | adherencedata | /api/v2/workforcemanagement/adherence | ✅ |
| [Chat](Chat.md) | Interaction | Hourly | chatdata, chatmessagedata | /api/v2/analytics/conversations | ✅ |
| [Evaluation](Evaluation.md) | Interaction | Daily | evaldata, evalscoredata | /api/v2/quality/evaluations | ✅ |
| [FactData](FactData.md) | Reference | Daily | userdetails, queuedetails | /api/v2/users, /api/v2/routing/queues | ✅ |
| [Information](Information.md) | Utility | On-demand | N/A (read-only) | System diagnostics | ✅ |
| [Install](Install.md) | Utility | One-time | All tables | Database schema creation | ✅ |
| [Interaction](Interaction.md) | Interaction | Hourly | conversationdata, participantdata | /api/v2/analytics/conversations | ✅ |
| [LicenseUsers](LicenseUsers.md) | Admin | Daily | licenseuserdata | /api/v2/license/users | ✅ |
| [Message](Message.md) | Interaction | Hourly | messagedata, messagecontentdata | /api/v2/analytics/conversations | ✅ |
| [OAuthUsage](OAuthUsage.md) | Admin | Daily | oauthusagedata | /api/v2/oauth/usage | ✅ |
| [QueueMembership](QueueMembership.md) | Admin | Daily | queuemembershipdata | /api/v2/routing/queues/{id}/members | ✅ |
| [Realtime](Realtime.md) | Real-time | Continuous | queuerealtimedata, userrealtimedata | WebSocket notifications | ✅ |
| [ScheduleDetails](ScheduleDetails.md) | WFM | Daily | scheduledetails, scheduleactivitydata | /api/v2/workforcemanagement/schedules | ✅ |
| [SubsUsers](SubsUsers.md) | Admin | ⚠️ DEPRECATED | subuserusagedata | ~~Billing API~~ (deprecated) | ✅ |
| [VoiceAnalysis](VoiceAnalysis.md) | Interaction | Hourly | convvoiceoverviewdata | /api/v2/analytics/conversations | ✅ |
| [WFMSchedule](WFMSchedule.md) | WFM | Daily | scheduledata | /api/v2/workforcemanagement/schedules | ✅ |

### Documentation Coverage Status
- **✅ Documented**: 16 jobs (43% of total 37 jobs)
- **❌ Missing Documentation**: 21 jobs (57% of total jobs)
- **⚠️ Deprecated**: 1 job (SubsUsers)

For detailed information about any specific job, click on the job name above or navigate to the corresponding documentation file.
