#nullable enable
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;           // Added for HttpWebRequest, HttpWebResponse, etc.
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using StandardUtils;
using OauthUsage = GenesysCloudDefOauthUsage;
using Subs = GenesysCloudDefSubscriptions;
using SubsOver = GenesysCloudDefSubscriptionOverView;
using SubUsers = GenesysCloudDefSubUsersUsage;
using Teams = GenesysCloudDefTeamDetailed;
using TeamsMembers = GenesysCloudDefTeamMembers;

namespace GenesysCloudUtils
{
    public class adminData
    {
        #region Fields and Properties

        public string CustomerKeyID { get; set; } = string.Empty;
        public string GCApiKey { get; set; } = string.Empty;
        public DataSet GCControlData { get; set; } = new DataSet();
        public string TimeZoneConfig { get; set; } = string.Empty;
        public string OAuthUser { get; set; } = string.Empty;

        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption = null!;
        private readonly GCUtils GCUtilities;
        private readonly JsonUtils JsonActions;

        private DataTable DownloadTable { get; set; } = new DataTable();
        private int TotalResponses { get; set; }
        private bool CanContinue { get; set; }

        private readonly ILogger? _logger;

        #endregion

        #region Constructors and Initialization

        public adminData() : this(null)
        {
        }

        public adminData(ILogger? logger)
        {
            _logger = logger;
            GCUtilities = new GCUtils(logger);
            JsonActions = new JsonUtils(logger);
        }

        public void Initialize()
        {
            _logger?.LogInformation("Initializing GenesysCloud adminData");
            GCUtilities.Initialize();
            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            _logger?.LogDebug("Obtaining Genesys Cloud API key");
            GCApiKey = GCUtilities.GCApiKey;
            _logger?.LogInformation("Initialization complete.");
        }

        #endregion

        #region System Call Usage

        public DataTable GetSystemCallUsage(int dayOffset)
        {
            _logger?.LogInformation("Starting GetSystemCallUsage for dayOffset: {DayOffset}", dayOffset);
            TimeZoneInfo appTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DBUtils.DBUtils dbUtil = new DBUtils.DBUtils();
            dbUtil.Initialize();

            DataTable systemCallUsageData = dbUtil.CreateInMemTable("systemcallusagedata");

            string fromDate = DateTime.Now.AddDays(-dayOffset).ToString("yyyy-MM-01T00:00:00", CultureInfo.InvariantCulture);
            string toDate = DateTime.Now.AddDays(-dayOffset).ToString("yyyy-MM-ddT23:59:59", CultureInfo.InvariantCulture);

            string query = $@"
                SELECT DISTINCT
                    conversationid,
                    conversationstartdateltc,
                    conversationenddateltc,
                    mediatype,
                    originaldirection,
                    purpose,
                    segmenttype,
                    segmentstartdateltc,
                    segmentenddateltc
                FROM detailedinteractiondata
                WHERE ((segmentstartdateltc < '{toDate}' AND segmentenddateltc IS NULL)
                    OR (segmentstartdateltc BETWEEN '{fromDate}' AND '{toDate}')
                    OR (segmentenddateltc BETWEEN '{fromDate}' AND '{toDate}'))
                    AND purpose NOT IN ('customer','external')
                    AND segmenttype NOT IN ('scheduled')";
            DataTable workingTable = dbUtil.GetSQLTableData(query, "detailedinteractionData");

            for (int minuteCounter = 1; minuteCounter < 1440; minuteCounter++)
            {
                string checkDate = DateTime.Parse(fromDate, CultureInfo.InvariantCulture)
                                    .AddMinutes(minuteCounter)
                                    .ToString("yyyy-MM-01THH:mm:00", CultureInfo.InvariantCulture);
                _logger?.LogDebug("Checking usage for timestamp: {CheckDate}", checkDate);

                string selectString = $"(segmentstartdateltc < '{checkDate}' and segmentenddateltc is null) " +
                                      $"or ('{checkDate}' >= segmentstartdateltc and '{checkDate}' <= segmentenddateltc)";

                DataRow[] drWorkingSet = workingTable.Select(selectString);
                string lastConversationId = string.Empty;

                foreach (DataRow segment in drWorkingSet)
                {
                    string conversationId = segment["conversationid"]?.ToString() ?? "";
                    if (conversationId != lastConversationId)
                    {
                        lastConversationId = conversationId;
                        DataRow systemCallLine = systemCallUsageData.Select(
                            $"rowdate = '{checkDate}' and mediatype = '{segment["mediatype"]}' and originaldirection = '{segment["originaldirection"]}' and purpose = '{segment["purpose"]}' and segmenttype = '{segment["segmenttype"]}'")
                            .FirstOrDefault() ?? systemCallUsageData.NewRow();

                        if (systemCallLine.RowState == DataRowState.Detached)
                        {
                            systemCallLine["keyid"] = $"{checkDate}|{segment["mediatype"]}|{segment["originaldirection"]}|{segment["purpose"]}|{segment["segmenttype"]}";
                            systemCallLine["rowdate"] = checkDate;
                            systemCallLine["mediatype"] = segment["mediatype"];
                            systemCallLine["originaldirection"] = segment["originaldirection"];
                            systemCallLine["purpose"] = segment["purpose"];
                            systemCallLine["segmenttype"] = segment["segmenttype"];
                            systemCallLine["liveinteractioncount"] = 0;
                            systemCallUsageData.Rows.Add(systemCallLine);
                        }
                        systemCallLine["liveinteractioncount"] = Convert.ToInt32(systemCallLine["liveinteractioncount"]) + 1;
                    }
                }
            }
            _logger?.LogInformation("GetSystemCallUsage completed. Total records: {Count}", systemCallUsageData.Rows.Count);
            return systemCallUsageData;
        }

        #endregion

        #region OAuth Usage

        public DataTable GetOauthUsage(int monthOffset)
        {
            _logger?.LogInformation("Starting GetOauthUsage for monthOffset: {MonthOffset}", monthOffset);
            TimeZoneInfo appTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DBUtils.DBUtils dbUtil = new DBUtils.DBUtils();
            dbUtil.Initialize();

            DataTable oauthUsageData = dbUtil.CreateInMemTable("oauthusageData");

            string fromDate = DateTime.Now.AddMonths(-monthOffset).ToString("yyyy-MM-01T00:00:00", CultureInfo.InvariantCulture);
            string toDate = DateTime.Parse(fromDate, CultureInfo.InvariantCulture)
                                .AddMonths(1)
                                .AddDays(-1)
                                .ToString("yyyy-MM-ddT23:59:59", CultureInfo.InvariantCulture);

            string jsonBody = $"{{\"interval\": \"{fromDate}/{toDate}\",\"granularity\": \"Day\",\"groupBy\": [\"OAuthClientId\"]}}";
            string urlExtra = "/api/v2/usage/query/";
            string uri = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            _logger?.LogDebug("Requesting OAuth usage report with interval: {Interval}", $"{fromDate}/{toDate}");

            HttpApiResponse apiResponse;
            try
            {
                apiResponse = JsonActions.JsonReturnHttpResponse(uri + urlExtra, GCApiKey, jsonBody);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "API Call Error while requesting OAuth usage report: {Message}", ex.Message);
                Console.WriteLine("Error requesting OAuth usage report: {0}", ex.Message);
                return oauthUsageData;
            }

            // Validate response using proper HTTP status code detection
            if (string.IsNullOrWhiteSpace(apiResponse.Content))
            {
                _logger?.LogWarning("Empty response received for OAuth usage report");
                Console.WriteLine("Warning: Empty response for OAuth usage report");
                return oauthUsageData;
            }

            // Handle different HTTP status codes appropriately
            if (!apiResponse.IsSuccess)
            {
                _logger?.LogError("API Error while requesting OAuth usage report: HTTP {StatusCode} - {StatusDescription}. Response: {Response}",
                    apiResponse.StatusCode, apiResponse.StatusDescription, apiResponse.Content);

                // Check for specific error types
                if (apiResponse.StatusCode == 403)
                {
                    _logger?.LogWarning("Permission denied (HTTP 403) when requesting OAuth usage report");
                    Console.WriteLine("Permission denied when requesting OAuth usage report");
                }
                else
                {
                    Console.WriteLine("API Error: HTTP {0} when requesting OAuth usage report", apiResponse.StatusCode);
                }
                return oauthUsageData;
            }

            OauthUsage.OauthUsageReport oauthRepData;
            try
            {
                oauthRepData = JsonConvert.DeserializeObject<OauthUsage.OauthUsageReport>(apiResponse.Content,
                    new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "JSON deserialization error for OAuth usage report: {Message}. Response content: {Content}", ex.Message, apiResponse.Content);
                Console.WriteLine("JSON error for OAuth usage report: {0}", ex.Message);
                return oauthUsageData;
            }

            if (apiResponse.Content.Length > 40 && !string.IsNullOrWhiteSpace(oauthRepData?.resultsUri))
            {
                _logger?.LogInformation("OAuth usage report started; pausing for report completion.");
                DateTime? firstRetryTime = null;
                int attempts = 0;
                const int maxAttempts = 7;
                bool successful = false;

                while (!successful && attempts < maxAttempts)
                {
                    attempts++;
                    _logger?.LogInformation("({Attempt}/{MaxAttempts}) Polling OAuth usage report: {JobId}", attempts, maxAttempts, oauthRepData.resultsUri);

                    HttpApiResponse resultsResponse;
                    try
                    {
                        resultsResponse = JsonActions.JsonReturnHttpResponseGet(uri + oauthRepData.resultsUri, GCApiKey);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "API Call Error while polling OAuth usage report: {Message}", ex.Message);
                        Console.WriteLine("Error polling OAuth usage report: {0}", ex.Message);
                        break;
                    }

                    // Validate response using proper HTTP status code detection
                    if (string.IsNullOrWhiteSpace(resultsResponse.Content))
                    {
                        _logger?.LogWarning("Empty response received while polling OAuth usage report");
                        Console.WriteLine("Warning: Empty response while polling OAuth usage report");
                        break;
                    }

                    // Handle different HTTP status codes appropriately
                    if (!resultsResponse.IsSuccess)
                    {
                        _logger?.LogError("API Error while polling OAuth usage report: HTTP {StatusCode} - {StatusDescription}. Response: {Response}",
                            resultsResponse.StatusCode, resultsResponse.StatusDescription, resultsResponse.Content);

                        // Check for specific error types
                        if (resultsResponse.StatusCode == 403)
                        {
                            _logger?.LogWarning("Permission denied (HTTP 403) while polling OAuth usage report");
                            Console.WriteLine("Permission denied while polling OAuth usage report");
                        }
                        else
                        {
                            Console.WriteLine("API Error: HTTP {0} while polling OAuth usage report", resultsResponse.StatusCode);
                        }
                        break;
                    }

                    OauthUsage.OauthUsageResult oauthResults;
                    try
                    {
                        oauthResults = JsonConvert.DeserializeObject<OauthUsage.OauthUsageResult>(resultsResponse.Content,
                            new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "JSON deserialization error while polling OAuth usage report: {Message}. Response content: {Content}", ex.Message, resultsResponse.Content);
                        Console.WriteLine("JSON error while polling OAuth usage report: {0}", ex.Message);
                        break;
                    }

                    if (!string.IsNullOrWhiteSpace(resultsResponse.Content) && resultsResponse.Content.Length > 30 && oauthResults?.queryStatus == "Complete")
                    {
                        successful = true;
                        _logger?.LogInformation("({Attempt}/{MaxAttempts}) OAuth usage report complete.", attempts, maxAttempts);
                        foreach (OauthUsage.Result oauthUsageDay in oauthResults.results)
                        {
                            try
                            {
                                DataRow oauthDay = oauthUsageData.NewRow();
                                oauthDay["clientid"] = oauthUsageDay.clientId;
                                oauthDay["status200"] = oauthUsageDay.status200;
                                oauthDay["status300"] = oauthUsageDay.status300;
                                oauthDay["status400"] = oauthUsageDay.status400;
                                oauthDay["status500"] = oauthUsageDay.status500;
                                oauthDay["status429"] = oauthUsageDay.status429;
                                oauthDay["organizationId"] = oauthUsageDay.organizationId;
                                oauthDay["userId"] = oauthUsageDay.userId;
                                oauthDay["rowdate"] = oauthUsageDay.date;
                                oauthDay["clientname"] = oauthUsageDay.clientName;
                                oauthDay["keyid"] = $"{oauthUsageDay.clientId}|{oauthUsageDay.date}";
                                oauthUsageData.Rows.Add(oauthDay);
                            }
                            catch (ConstraintException)
                            {
                                _logger?.LogDebug("Duplicate OAuth usage record skipped.");
                            }
                            catch (Exception ex)
                            {
                                _logger?.LogWarning(ex, "Error processing OAuth usage record: {Error}", ex.ToString());
                            }
                        }
                    }
                    else
                    {
                        if (firstRetryTime == null)
                        {
                            firstRetryTime = DateTime.UtcNow;
                        }
                        TimeSpan elapsed = DateTime.UtcNow - firstRetryTime.Value;
                        int delay;
                        if (elapsed < TimeSpan.FromMinutes(5))
                        {
                            delay = (attempts == 1) ? 1000 : 3000;
                        }
                        else if (elapsed < TimeSpan.FromMinutes(10))
                        {
                            delay = 9000;
                        }
                        else
                        {
                            delay = 27000;
                        }
                        _logger?.LogInformation("({Attempt}/{MaxAttempts}) OAuth usage report not complete; elapsed time: {Elapsed}. Waiting {Delay} ms before next attempt.", attempts, maxAttempts, elapsed, delay);
                        Thread.Sleep(delay);
                    }
                }
                if (!successful)
                {
                    _logger?.LogError("Exceeded maximum polling attempts for OAuth usage report.");
                }
            }
            else
            {
                _logger?.LogWarning("OAuth usage report request did not return a valid resultsUri.");
            }
            _logger?.LogInformation("GetOauthUsage completed. Total records: {Count}", oauthUsageData.Rows.Count);
            return oauthUsageData;
        }

        #endregion

        #region Subscription User Usage

        public DataTable GetSubUserUsage(DateTime dateToSyncFrom)
        {
            _logger?.LogError("DEPRECATED: GetSubUserUsage method has been decommissioned due to Genesys Cloud API deprecation. The /api/v2/billing/reports/hourlyLicenseUsageData endpoint was deprecated on March 10, 2025.");

            // Throw deprecation exception to ensure proper error handling
            throw new GenesysApiDeprecatedException(
                "The GetSubUserUsage method and underlying /api/v2/billing/reports/hourlyLicenseUsageData endpoint have been deprecated by Genesys Cloud as of March 10, 2025. " +
                "See: https://help.mypurecloud.com/announcements/deprecation-billing-and-usage-apis-and-user-interface/",
                "/api/v2/billing/reports/hourlyLicenseUsageData",
                405, // Method Not Allowed
                "{\"message\":\"HTTP 405 Method Not Allowed\",\"code\":\"method not allowed\",\"status\":405}",
                new DateTime(2025, 3, 10)
            );
        }

        #endregion

        #region License Users Data

        public DataTable GetLicenseUsersFromGC()
        {
            _logger?.LogInformation("Starting GetLicenseUsersFromGC");
            DBUtils.DBUtils dbUtil = new DBUtils.DBUtils();
            dbUtil.Initialize();

            DataTable licenseUserData = dbUtil.CreateInMemTable("licenseuserdata");
            string uri = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            int currentPage = 1;
            int pageSize = 50;
            bool hasMorePages = true;
            int totalRecordsProcessed = 0;

            while (hasMorePages)
            {
                string urlExtra = $"/api/v2/license/users?pageSize={pageSize}&pageNumber={currentPage}";

                _logger?.LogDebug("Requesting license users data - Page {CurrentPage}", currentPage);

                HttpApiResponse apiResponse;
                try
                {
                    apiResponse = JsonActions.JsonReturnHttpResponseGet(uri + urlExtra, GCApiKey);
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "API Call Error while requesting license users data: {Message}", ex.Message);
                    break;
                }

                // Validate response using proper HTTP status code detection
                if (string.IsNullOrWhiteSpace(apiResponse.Content))
                {
                    _logger?.LogWarning("Empty response received for license users data on page {CurrentPage}", currentPage);
                    break;
                }

                // Handle different HTTP status codes appropriately
                if (!apiResponse.IsSuccess)
                {
                    if (apiResponse.StatusCode == 403)
                    {
                        _logger?.LogWarning("Permission denied (HTTP 403) when requesting license users data. This feature may not be available with current permissions.");
                        break;
                    }
                    else if (apiResponse.StatusCode == 404)
                    {
                        _logger?.LogWarning("License users endpoint not found (HTTP 404). This API may not be available in this Genesys Cloud environment.");
                        break;
                    }
                    else
                    {
                        _logger?.LogError("API Error while requesting license users data: HTTP {StatusCode} - {StatusDescription}. Response: {Response}",
                            apiResponse.StatusCode, apiResponse.StatusDescription, apiResponse.Content);
                        break;
                    }
                }

                GenesysCloudDefLicenseUsers.LicenseUsersResponse licenseUsersResponse;
                try
                {
                    licenseUsersResponse = JsonConvert.DeserializeObject<GenesysCloudDefLicenseUsers.LicenseUsersResponse>(apiResponse.Content,
                        new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "JSON deserialization error while processing license users data: {Message}. Response content: {Content}",
                        ex.Message, apiResponse.Content);
                    break;
                }

                if (licenseUsersResponse?.entities != null)
                {
                    foreach (var user in licenseUsersResponse.entities)
                    {
                        if (user.licenses != null)
                        {
                            foreach (var licenseName in user.licenses)
                            {
                                try
                                {
                                    DataRow licenseRow = licenseUserData.NewRow();
                                    licenseRow["keyid"] = UCAUtils.GetStableHashCode($"{user.id}|{licenseName}");
                                    licenseRow["userid"] = user.id;
                                    licenseRow["username"] = DBNull.Value; // Not provided in /api/v2/license/users response
                                    licenseRow["email"] = DBNull.Value; // Not provided in /api/v2/license/users response
                                    licenseRow["licensename"] = licenseName;
                                    licenseRow["licensetype"] = DBNull.Value; // Not provided in /api/v2/license/users response
                                    licenseRow["licenseassigneddate"] = DBNull.Value; // Not provided in /api/v2/license/users response
                                    licenseRow["licenseexpirydate"] = DBNull.Value; // Not provided in /api/v2/license/users response
                                    licenseRow["licensestatus"] = DBNull.Value; // Not provided in /api/v2/license/users response
                                    licenseRow["organizationid"] = DBNull.Value; // Not provided in /api/v2/license/users response
                                    licenseRow["divisionid"] = DBNull.Value; // Not provided in /api/v2/license/users response
                                    licenseRow["updated"] = DateTime.UtcNow;
                                    licenseUserData.Rows.Add(licenseRow);
                                    totalRecordsProcessed++;
                                }
                                catch (ConstraintException)
                                {
                                    _logger?.LogDebug("Duplicate license user record skipped for user {UserId} and license {LicenseName}", user.id, licenseName);
                                }
                                catch (Exception ex)
                                {
                                    _logger?.LogWarning(ex, "Error processing license user record for user {UserId}: {Error}", user.id, ex.ToString());
                                }
                            }
                        }
                    }

                    // Check if there are more pages
                    hasMorePages = !string.IsNullOrWhiteSpace(licenseUsersResponse.nextUri);
                    currentPage++;

                    _logger?.LogDebug("Processed page {PageNumber} with {EntityCount} users. Total records so far: {TotalRecords}. More pages: {HasMorePages}",
                        currentPage - 1, licenseUsersResponse.entities.Length, totalRecordsProcessed, hasMorePages);
                }
                else
                {
                    _logger?.LogWarning("No license users data returned on page {CurrentPage}", currentPage);
                    hasMorePages = false;
                }
            }

            _logger?.LogInformation("GetLicenseUsersFromGC completed. Total license user records: {Count}", licenseUserData.Rows.Count);
            return licenseUserData;
        }

        #endregion

        #region Subscription Overview Data

        public DataTable GetSubscriptionOverViewDatafromGC()
        {
            _logger?.LogInformation("Starting GetSubscriptionOverViewDatafromGC");
            TimeZoneInfo appTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DBUtils.DBUtils dbUtil = new DBUtils.DBUtils();
            dbUtil.Initialize();

            DataTable subscriptionData = dbUtil.CreateInMemTable("suboverviewData");
            string uri = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            string periodBilling = JsonActions.JsonReturnString(uri + "/platform/api/v2/billing/periods?periodGranularity=month", GCApiKey);
            if (!string.IsNullOrWhiteSpace(periodBilling) && periodBilling != "{}")
            {
                dynamic billingPeriods = JsonConvert.DeserializeObject<dynamic>(periodBilling,
                    new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });

                // Check for Genesys Cloud API error responses (actual API error format)
                // Example: {"message":"No billing status found for org 62a87c35-e61a-4297-b6ae-2002ee71af4c","code":"resourcenotfounderror","status":404,...}
                if (billingPeriods.message != null && billingPeriods.status != null)
                {
                    string errorMessage = billingPeriods.message?.ToString() ?? "Unknown error";
                    string errorCode = billingPeriods.code?.ToString() ?? "Unknown";
                    int statusCode = billingPeriods.status ?? 0;

                    // ONLY handle gracefully if it's specifically the "No billing status found" error
                    if (statusCode == 404 && errorMessage.Contains("No billing status found"))
                    {
                        _logger?.LogWarning("No billing status found for this organization (HTTP 404). This may be expected for organizations without billing data. Error: {ErrorMessage}", errorMessage);
                        _logger?.LogInformation("GetSubscriptionOverViewDatafromGC completed with no data due to missing billing status. Total records: 0");
                        return subscriptionData; // Return empty DataTable - graceful exit
                    }
                    else
                    {
                        // For all other API errors, throw exception to maintain existing error behavior
                        _logger?.LogError("API error when retrieving billing periods: HTTP {StatusCode} - {ErrorCode} - {ErrorMessage}", statusCode, errorCode, errorMessage);
                        throw new InvalidOperationException($"API error when retrieving billing periods: HTTP {statusCode} - {errorCode} - {errorMessage}");
                    }
                }

                // Check for JsonReturnString error format (fallback for other error scenarios)
                // Example: {"error": true, "message": "Resource not found", "statusCode": "NotFound"}
                if (billingPeriods.error != null && billingPeriods.error == true)
                {
                    string errorMessage = billingPeriods.message?.ToString() ?? "Unknown error";
                    string statusCode = billingPeriods.statusCode?.ToString() ?? "Unknown";

                    // ONLY handle gracefully if it's a NotFound that could be related to billing status
                    if (statusCode == "NotFound")
                    {
                        _logger?.LogWarning("Resource not found when retrieving billing periods. This may be expected for organizations without billing data. Error: {ErrorMessage}", errorMessage);
                        _logger?.LogInformation("GetSubscriptionOverViewDatafromGC completed with no data due to missing billing status. Total records: 0");
                        return subscriptionData; // Return empty DataTable - graceful exit
                    }
                    else
                    {
                        // For all other error types, throw exception to maintain existing error behavior
                        _logger?.LogError("API error when retrieving billing periods: {StatusCode} - {ErrorMessage}", statusCode, errorMessage);
                        throw new InvalidOperationException($"API error when retrieving billing periods: {statusCode} - {errorMessage}");
                    }
                }

                // Check if entities property exists and is not null
                if (billingPeriods.entities == null)
                {
                    _logger?.LogWarning("Billing periods response does not contain entities data.");
                    _logger?.LogInformation("GetSubscriptionOverViewDatafromGC completed with no data. Total records: 0");
                    return subscriptionData; // Return empty DataTable
                }

                DateTime maxSyncDate = dbUtil.GetSyncLastUpdate("suboverviewdata");
                IEnumerable<dynamic> periodsEnumerable = ((IEnumerable<dynamic>)billingPeriods.entities)
                                                            .OrderBy(p => (DateTime)p.startDate);

                if (maxSyncDate != null && maxSyncDate != DateTime.MinValue)
                {
                    int lastPeriodIndex = -1;
                    int index = 0;
                    foreach (dynamic period in periodsEnumerable)
                    {
                        DateTime startDate = (DateTime)period.startDate;
                        DateTime endDate = (DateTime)period.endDate;
                        if (maxSyncDate >= startDate && maxSyncDate <= endDate)
                        {
                            lastPeriodIndex = index;
                            break;
                        }
                        index++;
                    }
                    if (lastPeriodIndex != -1)
                    {
                        periodsEnumerable = periodsEnumerable.Skip(Math.Max(0, lastPeriodIndex - 1));
                    }
                    else
                    {
                        periodsEnumerable = periodsEnumerable.Reverse().Take(1).Reverse();
                    }
                }

                var periodsArray = periodsEnumerable.ToArray();
                foreach (var period in periodsArray)
                {
                    string id = period.id.ToString();
                    string startDateStr = period.startDate.ToString();
                    string endDateStr = period.endDate.ToString();

                    _logger?.LogInformation("Processing billing period with ID: {Id}, StartDate: {StartDate}, EndDate: {EndDate}", id, startDateStr, endDateStr);
                    string jsonString = JsonActions.JsonReturnString(uri + "/api/v2/billing/subscriptionoverview?periodEndingTimestamp=" + id, GCApiKey);
                    SubsOver.SubOverview subsOverview = new SubsOver.SubOverview();
                    if (!string.IsNullOrWhiteSpace(jsonString) && jsonString != "{}")
                    {
                        subsOverview = JsonConvert.DeserializeObject<SubsOver.SubOverview>(jsonString,
                            new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                        DateTime rowDate = DateTime.UtcNow.Date;

                        if (subsOverview.usages != null && subsOverview.usages.Count() > 0)
                        {
                            foreach (SubsOver.Usage license in subsOverview.usages)
                            {
                                try
                                {
                                    DataRow licenseRow = subscriptionData.NewRow();
                                    licenseRow["keyid"] = $"{startDateStr}|{(string)license.partNumber}|{(string)license.name}|{(license.grouping != null ? license.grouping.ToString() : "")}";
                                    licenseRow["rowdate"] = rowDate;
                                    licenseRow["licname"] = license.name;
                                    licenseRow["partnumber"] = license.partNumber;
                                    licenseRow["grouping"] = license.grouping;
                                    licenseRow["unitofmeasuretype"] = license.unitOfMeasureType;
                                    licenseRow["usagequantity"] = license.usageQuantity;
                                    licenseRow["prepayQuantity"] = license.prepayQuantity ?? "0";
                                    licenseRow["overageprice"] = license.overagePrice != null ? Convert.ToDouble(license.overagePrice) : 0.0;
                                    licenseRow["iscancellable"] = license.isCancellable;
                                    licenseRow["isthirdparty"] = license.isThirdParty;
                                    licenseRow["startdate"] = startDateStr;
                                    licenseRow["enddate"] = endDateStr;
                                    subscriptionData.Rows.Add(licenseRow);
                                }
                                catch (ConstraintException)
                                {
                                    _logger?.LogDebug("Duplicate subscription overview record skipped for billing period {Id}.", id);
                                }
                                catch (Exception ex)
                                {
                                    _logger?.LogWarning(ex, "Error processing subscription overview record for billing period {Id}: {Error}", id, ex.ToString());
                                }
                            }
                            _logger?.LogInformation("Successfully processed billing period {Id} with {Count} usage records.", id, subsOverview.usages.Count());
                        }
                        else
                        {
                            _logger?.LogInformation("Billing period {Id} (Start: {StartDate}, End: {EndDate}) returned no usage records.", id, startDateStr, endDateStr);
                        }
                    }
                    else
                    {
                        _logger?.LogInformation("Billing period {Id} (Start: {StartDate}, End: {EndDate}) returned no data.", id, startDateStr, endDateStr);
                    }
                }
            }
            else
            {
                _logger?.LogWarning("No billing periods data returned from the API.");
            }
            _logger?.LogInformation("GetSubscriptionOverViewDatafromGC completed. Total records: {Count}", subscriptionData.Rows.Count);
            return subscriptionData;
        }

        #endregion

        #region Team Data

        public DataTable GetTeamMembersfromGC(DataTable teamDetails)
        {
            _logger?.LogInformation("Starting GetTeamMembersfromGC");
            DBUtils.DBUtils dbUtil = new DBUtils.DBUtils();
            dbUtil.Initialize();
            DataTable teamMembers = dbUtil.CreateInMemTable("teammemberdata");

            foreach (DataRow team in teamDetails.Rows)
            {
                bool nextPage = true;
                string nextPageStr = "";
                while (nextPage)
                {
                    try
                    {
                        string uri = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
                        string urlString = string.IsNullOrWhiteSpace(nextPageStr)
                            ? $"{uri}/api/v2/teams/{team["id"]}/members?expand=entities&pageSize=100"
                            : $"{uri}{nextPageStr}";
                        _logger?.LogDebug("Requesting team members for team ID: {TeamId} using URL: {UrlString}", team["id"], urlString);
                        string jsonString = JsonActions.JsonReturnString(urlString, GCApiKey);
                        TeamsMembers.Rootobject teamData = JsonConvert.DeserializeObject<TeamsMembers.Rootobject>(jsonString,
                            new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }) ?? new TeamsMembers.Rootobject();

                        if (!string.IsNullOrWhiteSpace(jsonString) && jsonString != "{}")
                        {
                            foreach (TeamsMembers.Entity user in teamData.entities)
                            {
                                DataRow teamRow = teamMembers.NewRow();
                                teamRow["keyid"] = $"{user.id}|{team["id"]}";
                                teamRow["teamid"] = team["id"];
                                teamRow["userid"] = user.id;
                                teamRow["updated"] = DateTime.UtcNow;
                                teamMembers.Rows.Add(teamRow);
                            }
                            if (!string.IsNullOrWhiteSpace(teamData.nextUri))
                            {
                                nextPageStr = teamData.nextUri;
                                _logger?.LogDebug("More team members available. Next page URI: {NextUri}", nextPageStr);
                            }
                            else
                            {
                                nextPage = false;
                            }
                        }
                        else
                        {
                            nextPage = false;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogWarning("Error retrieving team members for team ID {TeamId}: {Error}", team["id"], ex.ToString());
                        nextPage = false;
                    }
                }
            }
            _logger?.LogInformation("GetTeamMembersfromGC completed. Total team member records: {Count}", teamMembers.Rows.Count);
            return teamMembers;
        }

        public DataTable GetTeamDetailsfromGC()
        {
            _logger?.LogInformation("Starting GetTeamDetailsfromGC");
            DBUtils.DBUtils dbUtil = new DBUtils.DBUtils();
            dbUtil.Initialize();
            DataTable teamDetails = dbUtil.CreateInMemTable("teamdetails");
            try
            {
                string uri = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
                string jsonString = JsonActions.JsonReturnString(uri + "/api/v2/teams", GCApiKey);
                Teams.Teams teamData = JsonConvert.DeserializeObject<Teams.Teams>(jsonString,
                    new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }) ?? new Teams.Teams();

                if (!string.IsNullOrWhiteSpace(jsonString) && jsonString != "{}")
                {
                    foreach (Teams.Entity indTeam in teamData.entities)
                    {
                        DataRow teamRow = teamDetails.NewRow();
                        teamRow["id"] = indTeam.id;
                        teamRow["name"] = indTeam.name;
                        teamRow["division"] = indTeam.division;
                        teamRow["datecreated"] = indTeam.dateCreated;
                        teamRow["datemodified"] = indTeam.dateModified;
                        teamRow["membercount"] = indTeam.memberCount;
                        teamRow["updated"] = DateTime.UtcNow;
                        teamDetails.Rows.Add(teamRow);
                    }
                    _logger?.LogInformation("GetTeamDetailsfromGC completed. Total teams: {Count}", teamDetails.Rows.Count);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning("Error retrieving team details: {Error}", ex.ToString());
            }
            return teamDetails;
        }

        #endregion

        #region Helper Methods

        private decimal ConvertSeconds(string timeIn)
        {
            if (TimeSpan.TryParse(timeIn, out TimeSpan ts))
            {
                return (decimal)ts.TotalSeconds;
            }
            else
            {
                _logger?.LogWarning("Failed to parse time string: {TimeIn}", timeIn);
                return 0;
            }
        }

        #endregion
    }
}
