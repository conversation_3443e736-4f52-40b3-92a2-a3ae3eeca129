# Evaluation Job Documentation

## Overview

The **Evaluation** job synchronizes quality evaluation data from Genesys Cloud Quality Management. This job retrieves completed quality evaluations, evaluation forms, scores, and feedback data to support quality assurance programs, agent coaching, and performance management initiatives.

## Data Flow Architecture

:::mermaid
graph TB
    subgraph "Genesys Cloud Quality APIs"
        AUTH[OAuth Authentication<br/>/api/v2/oauth/token]
        EVAL[Evaluations API<br/>/api/v2/quality/evaluations]
        FORMS[Evaluation Forms<br/>/api/v2/quality/forms]
        SCORES[Evaluation Scores<br/>/api/v2/quality/evaluations/{id}]
        CONV[Conversations API<br/>/api/v2/conversations/{id}]
    end
    
    subgraph "Evaluation Job Process"
        EVL[GCUpdateEvaluationData<br/>Evaluation Controller]
        FETCH[Data Fetcher<br/>Evaluation Retrieval]
        PROC[Evaluation Processor<br/>Score Calculation]
        FORM[Form Processor<br/>Form Structure Analysis]
        LINK[Conversation Linker<br/>Interaction Association]
    end
    
    subgraph "Database Tables"
        ED[(evaldata<br/>Evaluation Records)]
        ESD[(evalscoredata<br/>Evaluation Scores)]
        EFD[(evalformdata<br/>Form Definitions)]
        EQD[(evalquestiondata<br/>Question Details)]
        ECD[(evalcommentdata<br/>Evaluator Comments)]
    end
    
    subgraph "Quality Features"
        SCORING[Score Calculation<br/>Weighted Scoring]
        CALIBRATION[Calibration<br/>Evaluator Consistency]
        FEEDBACK[Feedback Analysis<br/>Comment Processing]
        TRENDS[Trend Analysis<br/>Performance Tracking]
    end
    
    %% Main flow
    EVL --> AUTH
    AUTH --> FETCH
    FETCH --> EVAL
    FETCH --> FORMS
    EVAL --> PROC
    FORMS --> FORM
    PROC --> SCORES
    PROC --> CONV
    CONV --> LINK
    
    %% Quality processing
    PROC --> SCORING
    PROC --> CALIBRATION
    PROC --> FEEDBACK
    PROC --> TRENDS
    
    %% Database storage
    PROC --> ED
    SCORING --> ESD
    FORM --> EFD
    FORM --> EQD
    FEEDBACK --> ECD
    
    %% Styling
    classDef api fill:#e1f5fe
    classDef process fill:#e8f5e8
    classDef database fill:#fff3e0
    classDef quality fill:#f3e5f5
    
    class AUTH,EVAL,FORMS,SCORES,CONV api
    class EVL,FETCH,PROC,FORM,LINK process
    class ED,ESD,EFD,EQD,ECD database
    class SCORING,CALIBRATION,FEEDBACK,TRENDS quality
:::

## Configuration

### Basic Configuration
```json
{
  "Job": "Evaluation",
  "Preferences": {
    "Backfill": false,
    "MaxSyncSpan": "7.00:00",
    "LookBackSpan": "90.00:00"
  }
}
```

### Evaluation-Specific Options

#### Backfill Configuration
- **Purpose**: Synchronize historical evaluation data
- **Default**: `false`
- **Usage**: Set to `true` for initial data loads or historical analysis
- **Performance**: Large backfills may require extended processing time

#### Time Range Settings
- **MaxSyncSpan**: Maximum time range for single sync (default: 7 days)
- **LookBackSpan**: How far back to look for evaluations (default: 90 days)
- **Evaluation Window**: Evaluations can be completed days after conversation

## Database Schema

### Primary Tables

#### evaldata
- **Purpose**: Main evaluation records with summary information
- **Key Fields**: evaluationid, conversationid, evaluatorid, agentid, completeddate
- **Metrics**: Overall score, evaluation status, form version
- **Indexes**: evaluationid (PK), conversationid, agentid, completeddate

#### evalscoredata
- **Purpose**: Detailed scoring data for evaluation questions
- **Key Fields**: evaluationid, questionid, score, maxscore, weight
- **Calculations**: Weighted scores, section totals, overall percentages
- **Indexes**: evaluationid, questionid, score

#### evalformdata
- **Purpose**: Evaluation form definitions and structure
- **Key Fields**: formid, formname, version, questioncount, maxscore
- **Features**: Form versioning, question structure, scoring methodology
- **Indexes**: formid, version, formname

#### evalquestiondata
- **Purpose**: Individual question definitions and metadata
- **Key Fields**: formid, questionid, questiontext, questiontype, weight
- **Types**: Yes/No, Numeric, Multiple Choice, Text
- **Indexes**: formid, questionid, questiontype

#### evalcommentdata
- **Purpose**: Evaluator comments and feedback
- **Key Fields**: evaluationid, commenttype, commenttext, questionid
- **Categories**: Overall comments, question-specific feedback, coaching notes
- **Indexes**: evaluationid, commenttype, questionid

## API Integration

### Quality Management APIs

#### Evaluations API
- **Endpoint**: `/api/v2/quality/evaluations`
- **Method**: GET
- **Purpose**: Retrieve completed evaluations with filters
- **Pagination**: Handles large result sets with cursor-based pagination

#### Evaluation Forms API
- **Endpoint**: `/api/v2/quality/forms`
- **Purpose**: Get evaluation form definitions and structure
- **Versioning**: Handles multiple versions of evaluation forms

#### Query Parameters
```json
{
  "startTime": "2024-01-01T00:00:00Z",
  "endTime": "2024-01-07T23:59:59Z",
  "evaluatorUserId": "evaluator-id",
  "agentUserId": "agent-id",
  "formId": "form-id"
}
```

## Quality Management Features

### Evaluation Processing
- **Score Calculation**: Weighted scoring based on question importance
- **Form Versioning**: Handle multiple versions of evaluation forms
- **Status Tracking**: Track evaluation lifecycle from pending to completed
- **Conversation Linking**: Associate evaluations with specific interactions

### Quality Metrics
- **Overall Scores**: Calculated evaluation scores and percentages
- **Section Scores**: Scores by evaluation form sections
- **Question Analysis**: Performance on individual evaluation criteria
- **Trend Analysis**: Score trends over time for agents and teams

### Calibration Support
- **Evaluator Consistency**: Track scoring consistency across evaluators
- **Form Effectiveness**: Analyze evaluation form performance
- **Score Distribution**: Statistical analysis of evaluation scores
- **Bias Detection**: Identify potential evaluator bias patterns

## Dependencies

### Prerequisites
- **FactData**: User reference data for agents and evaluators
- **Interaction**: Conversation data for evaluation linking
- **Database Schema**: Evaluation tables must be installed
- **API Permissions**: Quality management read permissions

### Related Jobs
- **Interaction**: Provides conversation data linked to evaluations
- **VoiceAnalysis**: Voice analytics data may complement evaluation insights
- **FactData**: User and queue reference data for evaluation context
- **EvaluationCatchup**: Handles pending evaluations and updates

## Performance Optimization

### Evaluation-Specific Optimizations
- **Incremental Sync**: Only retrieve new or modified evaluations
- **Form Caching**: Cache evaluation form definitions to reduce API calls
- **Batch Processing**: Process evaluations in manageable batches
- **Conversation Linking**: Efficient linking of evaluations to conversations

### Score Calculation
- **Weighted Scoring**: Implement proper weighted score calculations
- **Section Aggregation**: Efficiently calculate section and overall scores
- **Historical Comparison**: Optimize queries for trend analysis

## Monitoring and Troubleshooting

### Key Metrics
- **Evaluations Processed**: Number of evaluations synchronized
- **Score Accuracy**: Validation of score calculations
- **Form Coverage**: Percentage of evaluations with complete form data
- **Conversation Linking**: Success rate of evaluation-conversation associations

### Common Issues

#### Missing Evaluations
- **Symptoms**: Evaluations visible in Genesys Cloud but not synchronized
- **Causes**: API permissions, date range issues, evaluation status filters
- **Solution**: Verify permissions and adjust query parameters

#### Score Calculation Errors
- **Symptoms**: Calculated scores don't match Genesys Cloud values
- **Causes**: Form versioning issues, weight calculation errors
- **Solution**: Validate form definitions and scoring methodology

#### Conversation Linking Failures
- **Symptoms**: Evaluations not linked to conversation data
- **Causes**: Conversation ID mismatches, timing issues
- **Solution**: Verify conversation data availability and ID consistency

### Evaluation-Specific Logging
- **Processing Progress**: Log evaluation processing status and counts
- **Score Calculations**: Log score calculation results and validation
- **Form Processing**: Log evaluation form processing and versioning

## Usage Examples

### Standard Evaluation Sync
```bash
GenesysAdapter --job Evaluation --config evaluation-production.json
```

### Historical Evaluation Backfill
```bash
GenesysAdapter --job Evaluation --backfill true --config evaluation-backfill.json
```

### Extended Lookback Period
```json
{
  "Job": "Evaluation",
  "Preferences": {
    "LookBackSpan": "180.00:00",
    "MaxSyncSpan": "14.00:00"
  }
}
```

## Best Practices

### Evaluation Data Management
- **Regular Sync**: Run evaluation job daily to capture completed evaluations
- **Extended Lookback**: Use longer lookback periods to catch late evaluations
- **Form Versioning**: Track evaluation form changes and version history

### Quality Assurance
- **Score Validation**: Regularly validate calculated scores against source data
- **Form Consistency**: Monitor evaluation form usage and effectiveness
- **Calibration Tracking**: Track evaluator consistency and calibration needs

### Performance Optimization
- **Incremental Processing**: Focus on new and modified evaluations
- **Efficient Linking**: Optimize conversation-evaluation linking processes
- **Batch Sizing**: Use appropriate batch sizes for evaluation processing

### Reporting and Analytics
- **Quality Dashboards**: Create dashboards for evaluation metrics and trends
- **Agent Performance**: Track individual agent evaluation performance
- **Calibration Reports**: Generate reports for evaluator calibration analysis
- **Form Effectiveness**: Analyze evaluation form performance and usage
