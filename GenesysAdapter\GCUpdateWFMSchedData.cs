using System;
using System.Data;
using Microsoft.Extensions.Logging;

namespace GenesysAdapter
{
    public class GCUpdateWFMSchedData
    {
        private readonly ILogger _logger;

        public GCUpdateWFMSchedData(ILogger logger)
        {
            _logger = logger;
        }

        public Boolean UpdateGCWFMSchedules(Boolean BackFill)
        {
            Boolean Successful = false;
            string SyncType = "scheduledata";

            try
            {
                DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
                DBAdapter.Initialize();

                GCData.GCGetData GCData = new GCData.GCGetData(_logger);
                GCData.Initialize(SyncType);

                DataSet ScheduleData = GCData.WFMScheduleData();

                if (ScheduleData != null && ScheduleData.Tables.Count > 0)
                {
                    foreach (DataTable table in ScheduleData.Tables)
                    {
                        if (table.Rows.Count > 0)
                        {
                            Successful = DBAdapter.WriteSQLData(table, table.TableName);
                            _logger?.LogInformation("Successfully processed {Count} {TableName} records", table.Rows.Count, table.TableName);
                        }
                        else
                        {
                            _logger?.LogInformation("{TableName}: No rows to update", table.TableName);
                        }
                    }

                    if (Successful)
                    {
                        Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, SyncType);
                    }
                    else
                    {
                        _logger?.LogWarning("Will not update the last update date for WFM Schedule Data - failure in processing");
                    }
                }
                else
                {
                    _logger?.LogInformation("WFM Schedule Data: No data returned from Genesys Cloud");
                    Successful = true; // Consider no data as successful to avoid blocking
                }

                // Explicitly close the database connection at the end of the job
                DBAdapter.CloseConnectionToDatabase();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error updating WFM schedule data: {Message}", ex.Message);
                Successful = false;
            }

            return Successful;
        }

        public Boolean UpdateScheduleDets()
        {
            Boolean Successful = false;
            string SyncType = "scheduledetails";

            try
            {
                DBUtils.DBUtils DBAdapterLocal = new DBUtils.DBUtils();
                DBAdapterLocal.Initialize();

                GCData.GCGetData GCData = new GCData.GCGetData(_logger);
                GCData.Initialize(SyncType);

                DataTable ScheduleDetailsData = GCData.WFMScheduleDetails();

                if (ScheduleDetailsData.Rows.Count > 0)
                {
                    Successful = DBAdapterLocal.WriteSQLData(ScheduleDetailsData, "scheduledetails");
                    _logger?.LogInformation("Successfully processed {Count} schedule details records", ScheduleDetailsData.Rows.Count);
                }
                else
                {
                    _logger?.LogInformation("Schedule details: No rows to update");
                }

                if (Successful)
                {
                    Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, "scheduledetails");
                }
                else
                {
                    _logger?.LogWarning("Will not update the last update date for Schedule Details Data - failure in processing");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error updating schedule details: {Message}", ex.Message);
                Successful = false;
            }

            return Successful;
        }

        public Boolean UpdateTimeOffRequests()
        {
            Boolean Successful = false;
            string SyncType = "timeoffrequests";

            try
            {
                DBUtils.DBUtils DBAdapterLocal = new DBUtils.DBUtils();
                DBAdapterLocal.Initialize();

                GCData.GCGetData GCData = new GCData.GCGetData(_logger);
                GCData.Initialize(SyncType);

                // Note: This method would need to be implemented in GCData if time off requests functionality is needed
                // For now, we'll just log that this functionality is not implemented
                _logger?.LogWarning("UpdateTimeOffRequests: Time off requests functionality is not currently implemented");
                
                // Return true to avoid breaking the job execution
                Successful = true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error updating time off requests: {Message}", ex.Message);
                Successful = false;
            }

            return Successful;
        }
    }
}
