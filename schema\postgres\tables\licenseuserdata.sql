CREATE TABLE IF NOT EXISTS licenseuserdata (
    keyid varchar(100) NOT NULL,
    userid varchar(50),
    username varchar(200),
    email varchar(200),
    licensename varchar(200),
    licensetype varchar(100),
    licenseassigneddate timestamp without time zone,
    licenseexpirydate timestamp without time zone,
    licensestatus varchar(50),
    organizationid varchar(50),
    divisionid varchar(50),
    updated timestamp without time zone,
    CONSTRAINT licenseuserdata_pkey PRIMARY KEY (keyid)
) TABLESPACE pg_default;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS licenseuserdata_userid ON licenseuserdata USING btree (userid ASC NULLS LAST) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS licenseuserdata_licensename ON licenseuserdata USING btree (licensename ASC NULLS LAST) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS licenseuserdata_updated ON licenseuserdata USING btree (updated ASC NULLS LAST) TABLESPACE pg_default;

-- Add table and column comments
COMMENT ON TABLE licenseuserdata IS 'License user data from Genesys Cloud /api/v2/license/users endpoint - replacement for deprecated subscription usage data';
COMMENT ON COLUMN licenseuserdata.keyid IS 'Primary key identifier (hash of userid + licensename)';
COMMENT ON COLUMN licenseuserdata.userid IS 'Genesys Cloud user ID';
COMMENT ON COLUMN licenseuserdata.username IS 'User display name (always null - not provided by /api/v2/license/users)';
COMMENT ON COLUMN licenseuserdata.email IS 'User email address (always null - not provided by /api/v2/license/users)';
COMMENT ON COLUMN licenseuserdata.licensename IS 'Name of the assigned license (from API response)';
COMMENT ON COLUMN licenseuserdata.licensetype IS 'Type/category of the license (always null - not provided by /api/v2/license/users)';
COMMENT ON COLUMN licenseuserdata.licenseassigneddate IS 'Date when the license was assigned to the user (always null - not provided by /api/v2/license/users)';
COMMENT ON COLUMN licenseuserdata.licenseexpirydate IS 'Date when the license expires (always null - not provided by /api/v2/license/users)';
COMMENT ON COLUMN licenseuserdata.licensestatus IS 'Status of the license (always null - not provided by /api/v2/license/users)';
COMMENT ON COLUMN licenseuserdata.organizationid IS 'Genesys Cloud organization ID (always null - not provided by /api/v2/license/users)';
COMMENT ON COLUMN licenseuserdata.divisionid IS 'Genesys Cloud division ID (always null - not provided by /api/v2/license/users)';
COMMENT ON COLUMN licenseuserdata.updated IS 'Date row was last updated (UTC)';
