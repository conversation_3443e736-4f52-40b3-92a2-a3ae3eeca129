# FactData Job Documentation

## Overview

The FactData job synchronizes reference data from Genesys Cloud, providing lookup tables and configuration data that support other jobs. It maintains essential organizational structure information including users, queues, business units, and activity codes.

## Data Flow Diagram

:::mermaid
graph TB
    subgraph "Genesys Cloud APIs"
        USERS[Users API<br/>/api/v2/users]
        QUEUES[Queues API<br/>/api/v2/routing/queues]
        BU[Business Units<br/>/api/v2/businessunits]
        GROUPS[Groups API<br/>/api/v2/groups]
        SKILLS[Skills API<br/>/api/v2/routing/skills]
        ACTIVITIES[Activity Codes<br/>/api/v2/workforcemanagement/activitycodes]
    end

    subgraph "FactData Job Process"
        FD[GCUpdateFactTables<br/>Fact Data Controller]
        ROUTER[Job Router<br/>Sub-job Selection]
        PARALLEL[Parallel Processor<br/>Concurrent Execution]
        DEPS[Dependency Manager<br/>Execution Order]
    end

    subgraph "Sub-Jobs"
        subgraph "Core Reference Data"
            USERS_JOB[Users & Groups]
            QUEUES_JOB[Queue Details]
            BU_JOB[Business Units]
        end

        subgraph "WFM Reference Data"
            ACTIVITIES_JOB[Activity Codes]
            MU_JOB[Management Units]
            PLANNING_JOB[Planning Groups]
        end

        subgraph "Skills & Mappings"
            SKILLS_JOB[Skill Details]
            MAPPINGS_JOB[User-Skill Mappings]
            QUEUE_MAP[User-Queue Mappings]
        end
    end

    subgraph "Database Tables"
        UD[(userdetails)]
        QD[(queuedetails)]
        BD[(budetails)]
        GD[(groupdetails)]
        SD[(skilldetails)]
        AD[(activitycodedetails)]
        USM[(userskillmappings)]
        UQM[(userqueuemappings)]
    end

    %% Main flow
    FD --> ROUTER
    ROUTER --> PARALLEL
    PARALLEL --> DEPS

    %% Sub-job execution
    DEPS --> USERS_JOB
    DEPS --> QUEUES_JOB
    DEPS --> BU_JOB
    DEPS --> ACTIVITIES_JOB
    DEPS --> MU_JOB
    DEPS --> PLANNING_JOB
    DEPS --> SKILLS_JOB
    DEPS --> MAPPINGS_JOB
    DEPS --> QUEUE_MAP

    %% API connections
    USERS_JOB --> USERS
    QUEUES_JOB --> QUEUES
    BU_JOB --> BU
    USERS_JOB --> GROUPS
    SKILLS_JOB --> SKILLS
    ACTIVITIES_JOB --> ACTIVITIES

    %% Database connections
    USERS_JOB --> UD
    USERS_JOB --> GD
    QUEUES_JOB --> QD
    BU_JOB --> BD
    SKILLS_JOB --> SD
    ACTIVITIES_JOB --> AD
    MAPPINGS_JOB --> USM
    QUEUE_MAP --> UQM

    %% Styling
    classDef api fill:#e1f5fe
    classDef process fill:#e8f5e8
    classDef subjob fill:#fff3e0
    classDef database fill:#f3e5f5

    class USERS,QUEUES,BU,GROUPS,SKILLS,ACTIVITIES api
    class FD,ROUTER,PARALLEL,DEPS process
    class USERS_JOB,QUEUES_JOB,BU_JOB,ACTIVITIES_JOB,MU_JOB,PLANNING_JOB,SKILLS_JOB,MAPPINGS_JOB,QUEUE_MAP subjob
    class UD,QD,BD,GD,SD,AD,USM,UQM database
:::

## Configuration

### Required Settings
- **GenesysApi**: OAuth credentials with appropriate permissions
- **Database**: Connection string for target database

### Optional Settings
- **FactDataJobs**: Specific sub-jobs to run (default: All)
- **ParallelExecution**: Enable concurrent processing
- **IncludeInactive**: Include disabled entities

### Example Configuration
```json
{
  "Job": "FactData",
  "GenesysApi": {
    "ClientId": "your-client-id",
    "ClientSecret": "your-client-secret",
    "Region": "us-east-1"
  },
  "Preferences": {
    "FactDataJobs": ["All"]
  }
}
```

### Specific Sub-Jobs
```json
{
  "Job": "FactData",
  "Preferences": {
    "FactDataJobs": [
      "BUDetails",
      "ActivityCodeDetails", 
      "SkillDetails"
    ]
  }
}
```

## Sub-Job Types

### Core Reference Data
- **BUDetails**: Business unit hierarchy and configuration
- **DivisionDetails**: Division structure and assignments
- **GroupDetails**: User groups and membership
- **UserDetails**: User profiles and attributes

### Queue Management
- **QueueDetails**: Queue configuration and settings
- **ServiceGoalDetails**: Service level objectives
- **WrapupDetails**: Wrap-up code definitions

### Workforce Management
- **ActivityCodeDetails**: WFM activity definitions
- **MUDetails**: Management unit configuration
- **PlanningGroupDetails**: Planning group structure

### Skills and Routing
- **SkillDetails**: Routing skill definitions
- **SkillsMapping**: User skill assignments
- **UserQueueMapping**: Agent queue assignments

### Knowledge and Learning
- **KnowledgeBaseDetails**: Knowledge base configuration
- **LearningDataDetails**: Learning module structure

## Database Tables

### Core Tables
- **userdetails**: User profiles and attributes
- **queuedetails**: Queue configuration
- **budetails**: Business unit hierarchy
- **groupdetails**: User group definitions

### Mapping Tables
- **userskillmappings**: User-to-skill assignments
- **userqueuemappings**: User-to-queue assignments
- **usergroupmappings**: User-to-group assignments

### Reference Tables
- **activitycodedetails**: WFM activity codes
- **skilldetails**: Routing skill definitions
- **divisiondetails**: Organizational divisions

## Processing Logic

### Execution Order
1. **Core Entities**: Business units, divisions, users
2. **Organizational Structure**: Groups, queues, skills
3. **Mappings**: User assignments and relationships
4. **Specialized Data**: Activity codes, knowledge bases

### Dependency Management
- Users must exist before processing user mappings
- Business units required before queue assignments
- Skills needed before skill mappings
- Groups processed before group memberships

### Error Handling
- Continue processing other sub-jobs if one fails
- Log detailed error information for troubleshooting
- Maintain referential integrity across related tables

## Monitoring

### Key Metrics
- **Sub-Jobs Completed**: Success rate per sub-job type
- **Records Processed**: Count per table/entity type
- **Processing Time**: Duration per sub-job
- **API Calls**: Number of requests per endpoint

### Performance Indicators
- **Throughput**: Records processed per minute
- **API Efficiency**: Response time per endpoint
- **Database Performance**: Bulk operation timing
- **Memory Usage**: Peak memory during processing

## Troubleshooting

### Common Issues

#### Partial Failures
- **Symptoms**: Some sub-jobs succeed, others fail
- **Causes**: Permission issues, API rate limits
- **Solutions**: Check OAuth scopes, retry failed sub-jobs

#### Mapping Inconsistencies
- **Symptoms**: Orphaned records in mapping tables
- **Causes**: Missing parent entities, timing issues
- **Solutions**: Run core jobs first, verify dependencies

#### Performance Issues
- **Symptoms**: Long execution times, timeouts
- **Causes**: Large datasets, sequential processing
- **Solutions**: Enable parallel execution, optimize queries

## Dependencies

### Prerequisites
- **OAuth Permissions**: Various scopes depending on sub-jobs
- **Database Schema**: All fact tables must exist
- **Network Connectivity**: Stable connection for API calls

### Related Jobs
- **All Jobs**: Most jobs depend on fact data for lookups
- **Users**: Provides detailed user information
- **QueueMembership**: Uses queue and user data

## Performance Optimization

### Best Practices
- Run during off-peak hours for large organizations
- Use parallel execution for independent sub-jobs
- Monitor API rate limits and adjust timing
- Optimize database indexes for lookup performance

### Scaling Considerations
- Large organizations may need longer processing windows
- Consider memory usage for organizations with complex hierarchies
- Monitor database performance during bulk operations

## Examples

### Run All Fact Data Jobs
```bash
GenesysAdapter --job FactData
```

### Specific Sub-Jobs Only
```bash
GenesysAdapter --job FactData --preferences.factdatajobs BUDetails,ActivityCodeDetails
```

### Parallel Processing
```json
{
  "Job": "FactData",
  "Preferences": {
    "ParallelExecution": true,
    "FactDataJobs": ["All"]
  }
}
```

### Include Inactive Entities
```json
{
  "Job": "FactData",
  "Preferences": {
    "IncludeInactive": true,
    "FactDataJobs": ["UserDetails", "QueueDetails"]
  }
}
```
