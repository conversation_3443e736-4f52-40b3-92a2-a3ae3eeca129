IF dbo.csg_table_exists('JobMinimumDefinition') = 0
BEGIN
    CREATE TABLE JobMinimumDefinition (
        JobID INT IDENTITY(1,1) PRIMARY KEY,
        JobName VARCHAR(255) UNIQUE,
        MaxSyncSpan VARCHAR(50),
        LookBackSpan VARCHAR(50)
    );
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'jobname_unique' AND object_id = OBJECT_ID('jobminimumdefinition'))
BEGIN
    ALTER TABLE jobminimumdefinition
    ADD CONSTRAINT jobname_unique UNIQUE (JobName);
END;

WITH CTE AS (
    SELECT *, 
           ROW_NUMBER() OVER (PARTITION BY JobName ORDER BY (SELECT NULL)) AS RowNum
    FROM jobminimumdefinition
)
DELETE FROM CTE WHERE RowNum > 1;

MERGE JobMinimumDefinition AS target
USING (VALUES
    ('Adherence', '7.00:00', '14.00:00'),
    ('Aggregation', '1.00:00', '0.08:00'),
    ('Chat', '1.00:00', '0.08:00'),
    ('Evaluation', '7.00:00', '90.00:00'),
    ('EvaluationCatchup', '1.00:00', '0.08:00'),
    ('FactData', '1.00:00', '0.08:00'),
    ('HeadCountForecast', '1.00:00', '0.08:00'),
    ('HoursBlockData', '1.00:00', '0.08:00'),
    ('Information', '1.00:00', '0.08:00'),
    ('Install', '1.00:00', '0.08:00'),
    ('Interaction', '1.00:00', '0.08:00'),
    ('InteractionPresence', '1.00:00', '0.08:00'),
    ('Knowledge', '1.00:00', '0.08:00'),
    ('Message', '1.00:00', '0.08:00'),
    ('OAuthUsage', '1.00:00', '0.08:00'),
    ('ODContactLists', '1.00:00', '5000.00:00'),
    ('ODDetails', '1.00:00', '0.08:00'),
    ('OfferedForecast', '1.00:00', '0.08:00'),
    ('PresenceDetail', '1.00:00', '0.08:00'),
    ('QueueMembership', '1.00:00', '0.08:00'),
    ('Realtime', '1.00:00', '0.08:00'),
    ('ScheduleDetails', '1.00:00', '0.08:00'),
    ('Shrinkage', '1.00:00', '0.08:00'),
    ('Subscription', '1.00:00', '0.08:00'),
    ('SubsUsers', '1.00:00', '0.08:00'),
    ('Survey', '1.00:00', '0.08:00'),
    ('SysConvUsage', '1.00:00', '0.08:00'),
    ('TimeOffReq', '1.00:00', '0.08:00'),
    ('UserQueueAudit', '1.00:00', '0.08:00'),
    ('UserQueueMapping', '1.00:00', '0.08:00'),
    ('VoiceAnalysis', '1.00:00', '0.08:00'),
    ('WFMAudit', '1.00:00', '0.08:00'),
    ('WFMSchedule', '30.00:00', '30.00:00'),
    ('Learning', '1.00:00', '2.00:00')
) AS source (JobName, MaxSyncSpan, LookBackSpan)
ON target.JobName = source.JobName
WHEN NOT MATCHED BY TARGET THEN
    INSERT (JobName, MaxSyncSpan, LookBackSpan)
    VALUES (source.JobName, source.MaxSyncSpan, source.LookBackSpan);

-- Disabled as unclear why it exists

-- UPDATE JobMinimumDefinition
-- SET LookBackSpan = 
--     CASE
--         WHEN FORMAT(GETDATE(), 'yy-MM-dd') = '24-12-19'
--              AND CAST(PARSENAME(LookBackSpan, 2) AS INT) < 4
--         THEN '4.00:00'
--         ELSE '0.08:00'
--     END
-- WHERE JobName = 'Interaction';


UPDATE JobMinimumDefinition
SET LookBackSpan = '90.00:00'
WHERE JobName = 'Evaluation'
  AND CAST(PARSENAME(LookBackSpan, 2) AS INT) < 90;


UPDATE JobMinimumDefinition
SET    LookBackSpan = '5000.00:00'
WHERE  JobName = 'ODContactLists'
  AND  LookBackSpan = '0.08:00';