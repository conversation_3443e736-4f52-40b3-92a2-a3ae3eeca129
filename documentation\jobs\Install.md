# Install Job Documentation

## Overview

The **Install** job is a critical system initialization job that sets up the complete database schema for the Genesys Adapter. This job creates all necessary tables, views, functions, and indexes across supported database platforms (MSSQL, PostgreSQL, Snowflake, Databricks), ensuring the adapter has the proper foundation for data synchronization operations.

## Data Flow Architecture

:::mermaid
graph TB
    subgraph "Database Platforms"
        MSSQL[(Microsoft SQL Server)]
        POSTGRES[(PostgreSQL)]
        SNOWFLAKE[(Snowflake)]
        DATABRICKS[(Databricks)]
    end
    
    subgraph "Install Job Process"
        INSTALL[GCInstallDatabase<br/>Install Controller]
        DETECT[Platform Detection<br/>Database Type Identification]
        SCHEMA[Schema Processor<br/>SQL Script Execution]
        VALIDATE[Validation Engine<br/>Installation Verification]
        UPGRADE[Upgrade Handler<br/>Schema Updates]
    end
    
    subgraph "Schema Components"
        TABLES[Table Creation<br/>Core Data Structures]
        VIEWS[View Creation<br/>Data Access Layers]
        FUNCTIONS[Function Installation<br/>Stored Procedures]
        INDEXES[Index Creation<br/>Performance Optimization]
        ALIASES[Alias Creation<br/>Backward Compatibility]
    end
    
    subgraph "Installation Features"
        CONDITIONAL[Conditional Logic<br/>Existing Installation Detection]
        MIGRATION[Schema Migration<br/>Version Upgrades]
        ROLLBACK[Rollback Support<br/>Error Recovery]
        VALIDATION_CHECK[Validation Checks<br/>Installation Verification]
    end
    
    %% Main flow
    INSTALL --> DETECT
    DETECT --> MSSQL
    DETECT --> POSTGRES
    DETECT --> SNOWFLAKE
    DETECT --> DATABRICKS
    
    %% Schema processing
    DETECT --> SCHEMA
    SCHEMA --> TABLES
    SCHEMA --> VIEWS
    SCHEMA --> FUNCTIONS
    SCHEMA --> INDEXES
    SCHEMA --> ALIASES
    
    %% Installation features
    SCHEMA --> CONDITIONAL
    SCHEMA --> MIGRATION
    SCHEMA --> ROLLBACK
    SCHEMA --> VALIDATION_CHECK
    VALIDATE --> VALIDATION_CHECK
    
    %% Upgrade handling
    CONDITIONAL --> UPGRADE
    UPGRADE --> MIGRATION
    
    %% Styling
    classDef database fill:#e1f5fe
    classDef process fill:#e8f5e8
    classDef schema fill:#fff3e0
    classDef feature fill:#f3e5f5
    
    class MSSQL,POSTGRES,SNOWFLAKE,DATABRICKS database
    class INSTALL,DETECT,SCHEMA,VALIDATE,UPGRADE process
    class TABLES,VIEWS,FUNCTIONS,INDEXES,ALIASES schema
    class CONDITIONAL,MIGRATION,ROLLBACK,VALIDATION_CHECK feature
:::

## Configuration

### Basic Configuration
```json
{
  "Job": "Install",
  "DatabaseType": "PostgreSQL",
  "ConnectionString": "Host=localhost;Database=genesys;Username=user;Password=****"
}
```

### Installation Options

#### Database Platform Selection
- **MSSQL**: Microsoft SQL Server (2016 or later)
- **PostgreSQL**: PostgreSQL (12 or later)
- **Snowflake**: Snowflake Data Cloud
- **Databricks**: Databricks SQL Analytics

#### Installation Modes
- **Fresh Install**: Complete new installation
- **Upgrade**: Update existing schema to latest version
- **Repair**: Fix corrupted or incomplete installations
- **Validate**: Verify existing installation integrity

## Database Schema Components

### Core Tables (Created by Install)

#### System Tables
- **tabledefinitions**: Schema metadata and version tracking
- **viewdefinitions**: View definitions and dependencies
- **installlog**: Installation history and audit trail

#### User and Organization Tables
- **userdetails**: User information and attributes
- **queuedetails**: Queue configuration and membership
- **budetails**: Business unit structure
- **groupdetails**: User group definitions

#### Interaction Tables
- **conversationdata**: Main conversation records
- **participantdata**: Conversation participants
- **segmentdata**: Media segment details
- **attributedata**: Custom attributes

#### WFM Tables
- **scheduledata**: Agent schedules
- **scheduledetails**: Detailed schedule information
- **adherencedata**: Schedule adherence tracking
- **wfmauditdata**: WFM change audit trail

#### Real-time Tables
- **queuerealtimedata**: Live queue metrics
- **userrealtimedata**: Live user status
- **queuerealtimeconvdata**: Live conversation data

### Views and Functions

#### Data Access Views
- **vwconvSummaryData**: Conversation summary with joins
- **vwuserDetails**: Enhanced user information
- **vwqueueMetrics**: Queue performance metrics

#### Utility Functions
- **csg_table_exists()**: Check table existence
- **csg_column_exists()**: Check column existence
- **csg_index_exists()**: Check index existence
- **csg_get_table_version()**: Get table version information

## Installation Process

### Pre-Installation Checks
1. **Database Connectivity**: Verify connection to target database
2. **Permissions**: Validate required database permissions
3. **Platform Detection**: Identify database platform and version
4. **Existing Schema**: Check for existing Genesys Adapter installation

### Installation Steps
1. **System Tables**: Create core system and metadata tables
2. **Reference Tables**: Install user, queue, and organizational tables
3. **Interaction Tables**: Create conversation and interaction tables
4. **WFM Tables**: Install workforce management tables
5. **Real-time Tables**: Create real-time data tables
6. **Views**: Install data access views
7. **Functions**: Create utility functions and procedures
8. **Indexes**: Create performance optimization indexes
9. **Validation**: Verify installation completeness

### Post-Installation Tasks
1. **Version Recording**: Record installation version in tabledefinitions
2. **Permission Setup**: Configure database user permissions
3. **Initial Data**: Insert default configuration data
4. **Validation Report**: Generate installation validation report

## Platform-Specific Features

### Microsoft SQL Server
- **T-SQL Syntax**: Uses SQL Server-specific syntax and features
- **Identity Columns**: Auto-incrementing primary keys
- **Clustered Indexes**: Optimized index structures
- **Stored Procedures**: T-SQL stored procedures for utilities

### PostgreSQL
- **Case Sensitivity**: Lowercase table and column names
- **Serial Columns**: PostgreSQL auto-increment syntax
- **JSONB Support**: Native JSON data type support
- **Extensions**: PostGIS and other extension support

### Snowflake
- **Cloud Architecture**: Optimized for cloud data warehouse
- **Clustering Keys**: Snowflake-specific clustering
- **Time Travel**: Built-in data versioning support
- **Secure Views**: Enhanced security features

### Databricks
- **Delta Lake**: Delta Lake table format
- **Spark SQL**: Spark SQL syntax and optimizations
- **Unity Catalog**: Databricks governance features
- **Photon Engine**: Performance optimizations

## Dependencies

### Prerequisites
- **Database Platform**: Supported database system installed and accessible
- **Permissions**: Database creation and modification permissions
- **Network Access**: Connectivity to database server
- **Disk Space**: Sufficient storage for schema and data

### No Job Dependencies
- **Independent**: Install job has no dependencies on other jobs
- **Foundation**: All other jobs depend on successful installation
- **Self-Contained**: Includes all necessary schema definitions

## Performance Considerations

### Installation Optimization
- **Batch Execution**: Execute schema scripts in optimized batches
- **Transaction Management**: Use transactions for rollback capability
- **Parallel Processing**: Create independent objects in parallel where possible
- **Index Creation**: Defer index creation until after table population

### Resource Management
- **Memory Usage**: Optimize memory usage during large schema creation
- **Disk I/O**: Minimize disk I/O during installation process
- **Lock Management**: Minimize database locks during installation

## Monitoring and Troubleshooting

### Installation Monitoring
- **Progress Tracking**: Monitor installation progress and completion
- **Error Detection**: Identify and report installation errors
- **Performance Metrics**: Track installation time and resource usage
- **Validation Results**: Report schema validation outcomes

### Common Issues

#### Permission Errors
- **Symptoms**: Access denied errors during schema creation
- **Causes**: Insufficient database permissions
- **Solution**: Grant appropriate database creation and modification permissions

#### Platform Compatibility
- **Symptoms**: SQL syntax errors during installation
- **Causes**: Platform-specific SQL syntax differences
- **Solution**: Verify database platform detection and use correct schema files

#### Existing Schema Conflicts
- **Symptoms**: Object already exists errors
- **Causes**: Previous installation or conflicting objects
- **Solution**: Use upgrade mode or clean existing schema

### Installation Logging
- **Detailed Logs**: Comprehensive logging of installation steps
- **Error Reporting**: Detailed error messages with context
- **Validation Results**: Complete validation report with ****/fail status

## Usage Examples

### Fresh Installation
```bash
GenesysAdapter --job Install --config fresh-install.json
```

### Upgrade Existing Schema
```bash
GenesysAdapter --job Install --upgrade true --config upgrade.json
```

### Validate Installation
```bash
GenesysAdapter --job Install --validate true --config validation.json
```

### Platform-Specific Installation
```json
{
  "Job": "Install",
  "DatabaseType": "Snowflake",
  "ConnectionString": "account=myaccount;user=myuser;****word=******;db=genesys;schema=public"
}
```

## Best Practices

### Installation Planning
- **Backup Strategy**: Backup existing database before installation
- **Testing Environment**: Test installation in non-production environment first
- **Rollback Plan**: Prepare rollback procedures for failed installations
- **Documentation**: Document installation parameters and customizations

### Schema Management
- **Version Control**: Track schema versions and changes
- **Migration Planning**: Plan schema migrations for upgrades
- **Validation Testing**: Regularly validate schema integrity
- **Performance Monitoring**: Monitor schema performance after installation

### Security Considerations
- **Permission Management**: Use principle of least privilege for database access
- **Connection Security**: Use secure connection strings and authentication
- **Audit Trail**: Maintain audit trail of schema changes
- **Access Control**: Implement appropriate database access controls

### Maintenance
- **Regular Validation**: Periodically validate schema integrity
- **Performance Tuning**: Monitor and optimize database performance
- **Backup Procedures**: Implement regular backup procedures
- **Update Planning**: Plan for regular schema updates and maintenance
