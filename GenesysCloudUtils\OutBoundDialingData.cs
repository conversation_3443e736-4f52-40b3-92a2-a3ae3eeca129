using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.WebSockets;
using System.Text;
using System.Threading.Tasks;
using CSG.Common.ExtensionMethods;
using DBUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StandardUtils;

namespace GenesysCloudUtils
{
    public class OutBoundDialingData
    {
        private readonly ILogger? _logger;

        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        public DataSet GCControlData { get; set; }
        private readonly Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        private readonly GCUtils GCUtilities;
        private readonly JsonUtils JsonActions;
        public string TimeZoneConfig { get; set; }

        // Constants
        private const string ININ_OUTBOUND_ID = "inin-outbound-id";

        private readonly DBUtils.DBUtils DBUtil;
        public string OAuthUser { get; set; }

        public OutBoundDialingData()
        {
            GCUtilities = new GCUtils();
            JsonActions = new JsonUtils();
            DBUtil = new DBUtils.DBUtils();
        }

        public OutBoundDialingData(ILogger? logger)
        {
            _logger = logger;
            GCUtilities = new GCUtils(logger);
            JsonActions = new JsonUtils(logger);
            DBUtil = new DBUtils.DBUtils();
        }

        public void Initialize()
        {
            GCUtilities.Initialize();

            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            _logger?.LogInformation("Obtaining API Key");
            GCApiKey = GCUtilities.GCApiKey;

            DBUtil.Initialize();
        }

        public DataTable GetContactListsFromCC()
        {
            return GetContactListsFromCCAsync().GetAwaiter().GetResult();
        }

        public async Task<DataTable> GetContactListsFromCCAsync()
        {
            int defaultDynamicColumnLength = 100;   // TODO: Move to an option
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            string JSONString = String.Empty;
            // maxWaitForListData: Max time to wait between requesting a contact list download and it being available.
            TimeSpan maxWaitForListData = TimeSpan.FromSeconds(90);
            // maxDownloadTimeout: Max time to wait for individual CSV download
            TimeSpan maxDownloadTimeout = TimeSpan.FromMinutes(5);
            Dictionary<string, string> listData = new();

            // Tracking variables for summary logging
            var truncationSummary = new Dictionary<string, int>(); // Column name -> truncation count
            int totalTruncations = 0;
            int processedContactLists = 0;
            int failedContactLists = 0;

            DataTable ContactLists = DBUtil.CreateInMemTable("odcontactlistdata");
            DataTable ContactListDetails = DBUtil.GetSQLTableData("select * from odcontactlistdetails", "odcontactlistdetails");

            // Skip download processing if no contact lists need to be processed
            if (ContactListDetails.Rows.Count == 0)
            {
                _logger?.LogInformation("ODContactLists: No contact lists to process");
                return ContactLists;
            }

            _logger?.LogInformation("ODContactLists: Processing {ContactListCount} contact lists", ContactListDetails.Rows.Count);

            foreach (DataRow ContactList in ContactListDetails.Rows)
            {
                try
                {
                    string contactListId = ContactList["id"].ToString();
                    string contactListName = ContactList["name"]?.ToString() ?? "Unknown";

                    _logger?.LogInformation("ODContactLists: Processing contact list '{ContactListName}' (ID: {ContactListId})",
                        contactListName, contactListId);

                    var apiResponse = JsonActions.JsonReturnHttpResponse(URI + "/api/v2/outbound/contactlists/" + contactListId + "/export", GCApiKey, "");

                    // Update our API key if it was refreshed during rate limit handling
                    if (!string.IsNullOrEmpty(apiResponse.UpdatedApiKey))
                    {
                        GCApiKey = apiResponse.UpdatedApiKey;
                        _logger?.LogInformation("Updated API key for contact list '{ContactListName}' after token refresh during rate limit handling", contactListName);
                    }

                    // Check HTTP status code and response content
                    if (apiResponse.IsSuccess && !string.IsNullOrEmpty(apiResponse.Content) && apiResponse.Content.Length > 20)
                    {
                        listData.Add(contactListId, apiResponse.Content);
                    }
                    else
                    {
                        failedContactLists++;
                        // Only log errors at debug level to reduce verbosity
                        _logger?.LogDebug("Contact list export request failed for {ContactListId} with status {StatusCode}",
                            contactListId, apiResponse.StatusCode);
                    }

                    await Task.Delay(150);
                }
                catch (Exception ex)
                {
                    failedContactLists++;
                    _logger?.LogDebug(ex, "Error requesting contact list download: {ContactListId}",
                        ContactList["id"].ToString());
                }
            }

            var timer = System.Diagnostics.Stopwatch.StartNew();
            _logger?.LogInformation("Waiting up to {WaitTime} for list downloads to be available", maxWaitForListData);
            await Task.Delay(1000);

            foreach (DataRow ContactList in ContactListDetails.Rows)
            {
                string contactListId = ContactList["id"].ToString();

                if (listData.ContainsKey(contactListId))
                {
                    string jsonResponse = listData[contactListId];

                    // Check if the response is an error response or empty JSON
                    if (jsonResponse.Contains("\"error\"") || jsonResponse == "{}")
                    {
                        failedContactLists++;
                        _logger?.LogDebug("Skipping contact list {ContactListId} due to error response", contactListId);
                        continue; // Skip this contact list
                    }

                    try
                    {
                        ContactListObject? contactListObj = JsonConvert.DeserializeObject<ContactListObject>(
                            jsonResponse,
                            new JsonSerializerSettings
                            {
                                NullValueHandling = NullValueHandling.Ignore
                            });

                        if (contactListObj != null && contactListObj.id != null)
                        {
                            bool neededToRetry = false;
                            int retryCount = 0;
                            const int maxRetries = 10; // Limit retries to prevent infinite loops

                            do
                            {
                                try
                                {
                                    var apiResponse = JsonActions.JsonReturnHttpResponseGet(URI + "/api/v2/outbound/contactlists/" + contactListId + "/export", GCApiKey);

                                    // Update our API key if it was refreshed during rate limit handling
                                    if (!string.IsNullOrEmpty(apiResponse.UpdatedApiKey))
                                    {
                                        GCApiKey = apiResponse.UpdatedApiKey;
                                        _logger?.LogInformation("Updated API key for contact list {ContactListId} after token refresh during rate limit handling", contactListId);
                                    }

                                    // Check if the response is successful and has valid content
                                    if (apiResponse.IsSuccess && !string.IsNullOrEmpty(apiResponse.Content) && apiResponse.Content.Length > 20)
                                    {
                                        JSONString = apiResponse.Content;
                                        if (neededToRetry)
                                            _logger?.LogDebug("Contact list ID {ContactListId} ready after {RetryCount} retries",
                                                contactListId, retryCount);
                                        else
                                            await Task.Delay(150);

                                        break;
                                    }
                                    else
                                    {
                                        // Suppress individual failure logs - will be included in summary
                                        _logger?.LogDebug("Contact list {ContactListId} availability check failed (attempt {RetryCount})",
                                            contactListId, retryCount + 1);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    _logger?.LogDebug(ex, "Error checking contact list {ContactListId} availability (attempt {RetryCount})",
                                        contactListId, retryCount + 1);

                                    // Add extra delay for repeated failures to avoid overwhelming the API
                                    if (retryCount > 3)
                                    {
                                        await Task.Delay(5000); // 5 second additional delay
                                    }
                                }

                                if (timer.Elapsed > maxWaitForListData)
                                {
                                    failedContactLists++;
                                    _logger?.LogDebug("Contact list ID {ContactListId} timed out after {RetryCount} retries",
                                        contactListId, retryCount);
                                    break;
                                }

                                if (retryCount >= maxRetries)
                                {
                                    failedContactLists++;
                                    _logger?.LogDebug("Contact list ID {ContactListId} exceeded maximum retry attempts",
                                        contactListId);
                                    break;
                                }

                                neededToRetry = true;
                                retryCount++;
                                await Task.Delay(2500);
                            } while (true);

                            if (!string.IsNullOrEmpty(JSONString) && JSONString.Length > 20)
                            {
                                // Check if the response is an error response
                                if (JSONString.Contains("\"error\""))
                                {
                                    failedContactLists++;
                                    _logger?.LogDebug("Skipping contact list URL for {ContactListId} due to error response",
                                        contactListId);
                                    continue; // Skip this contact list
                                }
                            }
                            else
                            {
                                failedContactLists++;
                                _logger?.LogDebug("Contact list {ContactListId} export data is null or too short after {RetryCount} retries, skipping",
                                    contactListId, retryCount);
                                continue; // Skip this contact list
                            }

                            if (!string.IsNullOrEmpty(JSONString) && JSONString.Length > 20)
                            {
                                try
                                {
                                    ContactListUrl? contactListUrl = JsonConvert.DeserializeObject<ContactListUrl>(JSONString,
                                      new JsonSerializerSettings
                                      {
                                          NullValueHandling = NullValueHandling.Ignore
                                      });

                                    //Now Get the CSV File.
                                    _logger?.LogDebug("Getting CSV file from: {Uri}", contactListUrl?.uri);
                                    if (contactListUrl?.uri != null)
                                    {
                                        try
                                        {
                                            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(contactListUrl.uri);
                                            request.Method = WebRequestMethods.Http.Get;
                                            request.Headers.Add("Authorization", "Bearer " + GCApiKey);
                                            request.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip;
                                            // Set timeout for CSV download
                                            request.Timeout = (int)maxDownloadTimeout.TotalMilliseconds;
                                            request.ReadWriteTimeout = (int)maxDownloadTimeout.TotalMilliseconds;

                                            using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
                                            {
                                                Stream responseStream = response.GetResponseStream();
                                                StreamReader readStream = null;
                                                if (response.CharacterSet == null)
                                                {
                                                    readStream = new StreamReader(responseStream);
                                                }
                                                else
                                                {
                                                    readStream = new StreamReader(responseStream,
                                                    Encoding.GetEncoding(response.CharacterSet));
                                                }

                                                string CSVString = readStream.ReadToEnd();

                                                response.Close();
                                                readStream.Close();

                                                DBUtils.GenUtils DBGenUtils = new DBUtils.GenUtils();

                                                DataTable DTContactList = DBGenUtils.ConvertCSVtoDataTable(CSVString, ContactList["NAME"].ToString());

                                                DataColumnCollection ContactListColumns = ContactLists.Columns;

                                                foreach (DataColumn CheckColumn in DTContactList.Columns)
                                                {
                                                    if (!ContactListColumns.Contains(CheckColumn.ColumnName.ToLower()))
                                                    {
                                                        DataColumn newCol = new(CheckColumn.ColumnName.ToLower(), typeof(String));
                                                        newCol.MaxLength = defaultDynamicColumnLength;
                                                        ContactLists.Columns.Add(newCol);
                                                    }
                                                }

                                                ContactLists.AcceptChanges();

                                                int contactRecordsAdded = 0;
                                                foreach (DataRow DRContact in DTContactList.Rows)
                                                {
                                                    string keyId = ContactList["id"] + "|" + DRContact[ININ_OUTBOUND_ID];
                                                    DataRow DRNewContact = ContactLists.NewRow();
                                                    DRNewContact["keyid"] = keyId;
                                                    DRNewContact["contactlistid"] = ContactList["id"];
                                                    DRNewContact[ININ_OUTBOUND_ID] = DRContact[ININ_OUTBOUND_ID];
                                                    DRNewContact["updated"] = DateTime.UtcNow;

                                                    foreach (DataColumn CheckColumn in DTContactList.Columns)
                                                    {
                                                        string ColumnName = CheckColumn.ColumnName.ToLower();

                                                        if (ColumnName != "keyid" && ColumnName != "updated" && ColumnName != ININ_OUTBOUND_ID)
                                                        {
                                                            if (ContactListColumns[ColumnName].DataType == typeof(string))
                                                            {
                                                                // TODO: Test removing this length code and relying on SetFieldValue.
                                                                // The length needs to be set when the column is added.
                                                                var colLength = ContactListColumns[ColumnName].MaxLength > 0 ? ContactListColumns[ColumnName].MaxLength : defaultDynamicColumnLength;
                                                                string val = DRContact[ColumnName]?.ToString() ?? "";
                                                                if (val.Length > colLength)
                                                                {
                                                                    // Track truncation for summary logging
                                                                    if (!truncationSummary.ContainsKey(ColumnName))
                                                                        truncationSummary[ColumnName] = 0;
                                                                    truncationSummary[ColumnName]++;
                                                                    totalTruncations++;

                                                                    val = val.Substring(0, colLength);
                                                                }
                                                                DRNewContact.SetFieldValue(keyId, ColumnName, val);
                                                            }
                                                            else
                                                            {
                                                                DRNewContact[ColumnName] = DRContact[ColumnName];
                                                            }
                                                        }
                                                    }

                                                    ContactLists.Rows.Add(DRNewContact);
                                                    contactRecordsAdded++;
                                                }

                                                // Log completion for this contact list
                                                string contactListName = ContactList["name"]?.ToString() ?? "Unknown";
                                                _logger?.LogInformation("ODContactLists: Completed '{ContactListName}' (ID: {ContactListId}) - {RecordCount} records added",
                                                    contactListName, contactListId, contactRecordsAdded);
                                                processedContactLists++;
                                            }
                                        }
                                        catch (WebException ex) when (ex.Status == WebExceptionStatus.Timeout)
                                        {
                                            _logger?.LogError(ex, "Timeout downloading CSV for contact list {ContactListId} after {Timeout} minutes",
                                                contactListId, maxDownloadTimeout.TotalMinutes);
                                            // Continue with the next contact list
                                            continue;
                                        }
                                        catch (WebException ex)
                                        {
                                            _logger?.LogError(ex, "Web exception downloading CSV for contact list {ContactListId}: {Status}",
                                                contactListId, ex.Status);
                                            // Continue with the next contact list
                                            continue;
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger?.LogError(ex, "Unexpected error downloading CSV for contact list {ContactListId}",
                                                contactListId);
                                            // Continue with the next contact list
                                            continue;
                                        }
                                    }
                                    else
                                    {
                                        _logger?.LogWarning("Contact list URL is null for {ContactListId}", contactListId);
                                    }
                                }
                                catch (Newtonsoft.Json.JsonException ex)
                                {
                                    _logger?.LogError(ex, "Failed to deserialize contact list URL for {ContactListId}: {Response}",
                                        contactListId, JSONString);
                                    // Continue with the next contact list
                                    continue;
                                }
                            }
                        }
                    }
                    catch (Newtonsoft.Json.JsonException ex)
                    {
                        _logger?.LogError(ex, "Failed to deserialize contact list {ContactListId} response: {Response}",
                            contactListId, jsonResponse);
                        // Continue with the next contact list
                        continue;
                    }
                }
            }

            // Log completion summary with truncation information
            var summaryMessage = $"ODContactLists: Processed {processedContactLists} of {ContactListDetails.Rows.Count} contact lists, retrieved {ContactLists.Rows.Count} contact records";

            if (failedContactLists > 0)
            {
                summaryMessage += $", {failedContactLists} failed";
            }

            // Only log truncation summary if truncation occurred
            if (totalTruncations > 0)
            {
                var truncationDetails = string.Join(", ", truncationSummary.Select(kvp => $"{kvp.Value} in {kvp.Key}"));
                summaryMessage += $", {totalTruncations} fields truncated ({truncationDetails})";
            }

            _logger?.LogInformation(summaryMessage);

            return ContactLists;
        }
    }

    public class ContactListObject
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class ContactListUrl
    {
        public string uri { get; set; }
        public DateTime exportTimestamp { get; set; }
    }
}
// spell-checker: ignore: contactlistid
