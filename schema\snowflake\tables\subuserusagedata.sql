-- Only create the subuserusagedata table if it doesn't exist and we're not in deprecation mode
-- Since this table is deprecated, we don't create it for new installations

-- Check if table exists and add deprecation columns if it does
CREATE OR REPLACE PROCEDURE handle_subuserusagedata_deprecation()
  RETURNS STRING
  LANGUAGE JAVASCRIPT
  EXECUTE AS CALLER
AS
$$
  try {
    // Check if table exists
    var checkTableQuery = `SELECT COUNT(*) as table_count
                          FROM information_schema.tables
                          WHERE table_name = 'SUBUSERUSAGEDATA'`;
    var resultSet = snowflake.execute({sqlText: checkTableQuery});
    resultSet.next();
    var tableExists = resultSet.getColumnValue(1) > 0;

    if (tableExists) {
      // Table exists, add deprecation columns for existing installations
      try {
        snowflake.execute({
          sqlText: `ALTER TABLE subuserusagedata
                   ADD COLUMN IF NOT EXISTS deprecated_status varchar(50) DEFAULT 'deprecated'`
        });
      } catch (e) {
        // Column might already exist, continue
      }

      try {
        snowflake.execute({
          sqlText: `ALTER TABLE subuserusagedata
                   ADD COLUMN IF NOT EXISTS deprecated_date timestamp without time zone DEFAULT '2025-03-10 00:00:00'`
        });
      } catch (e) {
        // Column might already exist, continue
      }

      return 'subuserusagedata table exists - added deprecation tracking columns';
    } else {
      // Table doesn't exist and is deprecated, so don't create it
      return 'subuserusagedata table does not exist and is deprecated - skipping table creation';
    }
  } catch (e) {
    return 'Error handling subuserusagedata deprecation: ' + e.message;
  }
$$;

-- Execute the procedure
CALL handle_subuserusagedata_deprecation();