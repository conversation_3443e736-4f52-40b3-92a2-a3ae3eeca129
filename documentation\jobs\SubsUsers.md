# SubsUsers Job Documentation ⚠️ **DEPRECATED**

## ⚠️ Deprecation Notice

**This job has been DECOMMISSIONED as of March 10, 2025 due to Genesys Cloud API deprecation.**

- **Status**: No longer functional
- **Replacement**: Use the [LicenseUsers](LicenseUsers.md) job instead
- **Impact**: Throws `GenesysApiDeprecatedException` when executed
- **Data**: Historical data preserved in `subuserusagedata` table

## Overview

The SubsUsers job previously retrieved subscription user usage data including license consumption and billing information from Genesys Cloud. It was used for monitoring user activity and license utilization before being replaced by the LicenseUsers job.

## Deprecation Flow Diagram

:::mermaid
graph TB
    subgraph "Deprecated API"
        OLD_API[❌ Billing API<br/>~~hourlyLicenseUsageData~~<br/>DEPRECATED March 10, 2025]
        OLD_ENDPOINT[❌ CSV Endpoint<br/>~~billing/reports/hourlyLicenseUsageData~~]
    end

    subgraph "SubsUsers Job (DEPRECATED)"
        SU[❌ GCUpdateAdminData<br/>SubsUsers Method]
        EXCEPTION[GenesysApiDeprecatedException<br/>Thrown on Execution]
    end

    subgraph "Migration Path"
        NEW_JOB[✅ LicenseUsers Job<br/>Replacement Functionality]
        NEW_API[✅ License API<br/>/api/v2/license/users]
        NEW_TABLE[✅ licenseuserdata<br/>Current License Assignments]
    end

    subgraph "Historical Data"
        OLD_TABLE[📊 subuserusagedata<br/>Historical Data Preserved]
        CONDITIONAL[⚙️ Conditional Schema<br/>Only in Existing Installations]
    end

    %% Deprecation flow
    SU --> OLD_API
    OLD_API --> EXCEPTION
    OLD_ENDPOINT --> EXCEPTION

    %% Migration flow
    SU -.-> NEW_JOB
    NEW_JOB --> NEW_API
    NEW_API --> NEW_TABLE

    %% Historical preservation
    SU -.-> OLD_TABLE
    OLD_TABLE --> CONDITIONAL

    %% Styling
    classDef deprecated fill:#ffcdd2,stroke:#d32f2f
    classDef exception fill:#ffebee,stroke:#f44336
    classDef migration fill:#e8f5e8,stroke:#4caf50
    classDef historical fill:#fff3e0,stroke:#ff9800

    class OLD_API,OLD_ENDPOINT,SU deprecated
    class EXCEPTION exception
    class NEW_JOB,NEW_API,NEW_TABLE migration
    class OLD_TABLE,CONDITIONAL historical
:::

## Historical Configuration

### Previous Settings (No Longer Valid)
```json
{
  "Job": "SubsUsers",  // ❌ DEPRECATED
  "GenesysApi": {
    "ClientId": "your-client-id",
    "ClientSecret": "your-client-secret",
    "Region": "us-east-1"
  }
}
```

## Historical Database Tables

### subuserusagedata (Preserved for Historical Data)
- **Purpose**: Historical subscription usage data
- **Status**: Read-only, no longer updated
- **Key Fields**:
  - `keyid` (varchar(100)): Primary key
  - `userid` (varchar(50)): User ID
  - `licensetype` (varchar(100)): License type
  - `usagedate` (datetime): Usage date
  - `usagehours` (decimal): Hours of usage
  - `updated` (datetime): Last update timestamp

### Schema Behavior
- **Existing Installations**: Table preserved with historical data
- **New Installations**: Table not created (conditional schema logic)
- **Updates**: No new data written to this table

## Migration to LicenseUsers

### Key Differences

| Aspect | SubsUsers (Deprecated) | LicenseUsers (Current) |
|--------|----------------------|----------------------|
| **API** | ❌ Billing CSV endpoint | ✅ `/api/v2/license/users` |
| **Data Type** | Historical usage hours | Current license assignments |
| **Update Frequency** | Hourly usage data | Real-time assignment status |
| **Performance** | CSV download + parsing | Direct JSON API |
| **Reliability** | Deprecated, unreliable | Active, supported |

### Migration Steps

1. **Stop Using SubsUsers**
   ```bash
   # ❌ This will fail
   GenesysAdapter --job SubsUsers
   ```

2. **Start Using LicenseUsers**
   ```bash
   # ✅ Use this instead
   GenesysAdapter --job LicenseUsers
   ```

3. **Update Reports**
   - Change queries from `subuserusagedata` to `licenseuserdata`
   - Adjust logic from usage hours to assignment status
   - Update dashboards and analytics

4. **Verify Data**
   - Compare license assignments between old and new data
   - Validate user coverage and accuracy
   - Test reporting functionality

## Error Handling

### Current Behavior
When SubsUsers job is executed, it will:

1. **Throw Exception**: `GenesysApiDeprecatedException`
2. **Log Error**: Clear deprecation message with migration guidance
3. **Fail Fast**: Job terminates immediately
4. **No Data Processing**: No attempt to access deprecated API

### Example Error
```
GenesysApiDeprecatedException: The SubsUsers job has been deprecated due to Genesys Cloud API changes. 
Please migrate to the LicenseUsers job for current license assignment data.
API Endpoint: /api/v2/billing/reports/hourlyLicenseUsageData (deprecated March 10, 2025)
Replacement: Use LicenseUsers job with /api/v2/license/users API
```

## Historical Context

### Why It Was Deprecated
- **Genesys Cloud Changes**: Billing infrastructure modernization
- **API Retirement**: CSV-based billing endpoints discontinued
- **Performance Issues**: CSV processing was slow and unreliable
- **Data Quality**: Usage hours vs current assignments mismatch

### Timeline
- **Before March 2025**: SubsUsers job functional
- **March 10, 2025**: Genesys Cloud deprecated billing API
- **Current**: Job throws deprecation exception
- **Future**: Complete removal from codebase planned

## Troubleshooting

### Common Issues

#### Job Execution Fails
- **Symptoms**: `GenesysApiDeprecatedException` thrown
- **Cause**: Job is deprecated and no longer functional
- **Solution**: Migrate to LicenseUsers job

#### Missing License Data
- **Symptoms**: No recent license usage data
- **Cause**: SubsUsers no longer updates data
- **Solution**: Use LicenseUsers for current license assignments

#### Report Errors
- **Symptoms**: Reports show stale or missing data
- **Cause**: Queries still reference deprecated subuserusagedata
- **Solution**: Update queries to use licenseuserdata table

### Migration Support

For assistance with migration:
1. Review [LicenseUsers job documentation](LicenseUsers.md)
2. Update job configurations to use LicenseUsers
3. Modify reports and dashboards accordingly
4. Test new functionality thoroughly

## Examples

### ❌ Deprecated Usage (Will Fail)
```bash
# This will throw GenesysApiDeprecatedException
GenesysAdapter --job SubsUsers
```

### ✅ Correct Migration
```bash
# Use this instead
GenesysAdapter --job LicenseUsers
```

### Query Migration
```sql
-- ❌ Old query (deprecated table)
SELECT userid, licensetype, SUM(usagehours) 
FROM subuserusagedata 
WHERE usagedate >= '2024-01-01'
GROUP BY userid, licensetype;

-- ✅ New query (current table)
SELECT userid, licensename, COUNT(*) as assignments
FROM licenseuserdata 
WHERE assigned = 1
GROUP BY userid, licensename;
```

## Related Documentation

- [LicenseUsers Job](LicenseUsers.md) - Replacement functionality
- [Migration Guide](../migration/SubsUsers-to-LicenseUsers.md) - Detailed migration steps
- [API Deprecation Handling](../architecture/API-Deprecation.md) - Framework overview
