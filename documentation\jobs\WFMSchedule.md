# WFM Schedule Job Documentation

## Overview

The WFM Schedule job retrieves workforce management schedule data from Genesys Cloud for long-term planning and analysis. It provides comprehensive agent scheduling information including shifts, activities, and time allocations for workforce optimization.

## Data Flow Diagram

:::mermaid
graph TB
    subgraph "Genesys Cloud APIs"
        AUTH[OAuth Authentication<br/>/api/v2/oauth/token]
        WFM[WFM Schedules API<br/>/api/v2/workforcemanagement/schedules]
        MU[Management Units<br/>/api/v2/workforcemanagement/managementunits]
    end

    subgraph "WFM Schedule Process"
        WS[GCUpdateWFMSchedData<br/>Schedule Controller]
        FETCH[Data Fetcher<br/>Schedule Retrieval]
        PROC[Schedule Processor<br/>Data Transformation]
        VALIDATE[Data Validator<br/>Schedule Validation]
        BATCH[Batch Writer<br/>Database Operations]
    end

    subgraph "Time Management"
        RANGE[Date Range Calculator<br/>26 weeks back/forward]
        CHUNK[Data Chunking<br/>30-day blocks]
        TIMEZONE[Timezone Handler<br/>UTC Conversion]
    end

    subgraph "Database Tables"
        SD[(scheduledata<br/>Agent Schedules)]
        SDD[(scheduledetails<br/>Schedule Details)]
        TD[(tabledefinitions<br/>Sync Tracking)]
    end

    subgraph "Related Data"
        UD[(userdetails<br/>Agent Information)]
        BU[(budetails<br/>Business Units)]
        AC[(activitycodedetails<br/>Activity Codes)]
    end

    %% Main flow
    WS --> AUTH
    AUTH --> FETCH
    FETCH --> WFM
    FETCH --> MU
    WFM --> PROC
    PROC --> VALIDATE
    VALIDATE --> BATCH
    BATCH --> SD

    %% Time management
    WS --> RANGE
    RANGE --> CHUNK
    CHUNK --> TIMEZONE
    TIMEZONE --> FETCH

    %% Related data connections
    UD -.-> VALIDATE
    BU -.-> VALIDATE
    AC -.-> VALIDATE

    %% Sync tracking
    BATCH --> TD

    %% Styling
    classDef api fill:#e1f5fe
    classDef process fill:#e8f5e8
    classDef time fill:#fff3e0
    classDef database fill:#f3e5f5
    classDef related fill:#fafafa

    class AUTH,WFM,MU api
    class WS,FETCH,PROC,VALIDATE,BATCH process
    class RANGE,CHUNK,TIMEZONE time
    class SD,SDD,TD database
    class UD,BU,AC related
:::

## Configuration

### Required Settings
- **GenesysApi**: OAuth credentials with WFM permissions
- **Database**: Connection string for target database
- **DateRange**: Schedule period (default: 26 weeks back/forward)

### Optional Settings
- **ManagementUnits**: Specific MUs to process (default: all)
- **ChunkSize**: Days per API call (default: 30)
- **Backfill**: Process historical schedules
- **TimeZone**: Target timezone for schedule data

### Example Configuration
```json
{
  "Job": "WFMSchedule",
  "GenesysApi": {
    "ClientId": "your-client-id",
    "ClientSecret": "your-client-secret",
    "Region": "us-east-1"
  },
  "Preferences": {
    "Backfill": false,
    "ChunkSize": 30
  }
}
```

## Database Tables

### scheduledata
- **Purpose**: Main agent schedule data with shifts and activities
- **Key Fields**:
  - `keyid` (varchar(100)): Primary key
  - `userid` (varchar(50)): Agent user ID
  - `shiftstartdate` (datetime): Shift start time
  - `shiftlengthtime` (int): Shift duration in seconds
  - `activitystartdate` (datetime): Activity start time
  - `activitylengthtime` (int): Activity duration in seconds
  - `activitydescription` (varchar(200)): Activity description
  - `activitycodeid` (varchar(50)): Activity code reference
  - `buid` (varchar(50)): Business unit ID
  - `scheduleid` (varchar(50)): Schedule identifier

### Indexes
- Primary key on `keyid`
- Index on `userid` for agent-based queries
- Index on `shiftstartdate` for time-based queries
- Index on `buid` for business unit reporting

## API Endpoints

### Primary APIs
- `/api/v2/workforcemanagement/schedules` - Schedule data retrieval
- `/api/v2/workforcemanagement/managementunits` - Management unit information
- `/api/v2/workforcemanagement/managementunits/{id}/schedules` - Unit-specific schedules

### Supporting APIs
- `/api/v2/oauth/token` - Authentication
- `/api/v2/users` - Agent validation

## Dependencies

### Prerequisites
- **OAuth Permissions**: `workforce-management:schedule:view`
- **Users Job**: Must run first to populate agent data
- **FactData**: Provides business unit and activity code data
- **Management Units**: Must be configured in Genesys Cloud

### Related Jobs
- **ScheduleDetails**: Provides detailed schedule breakdown
- **Adherence**: Uses schedule data for adherence calculations
- **HeadCountForecast**: Derives staffing predictions from schedules

## Processing Logic

### Date Range Calculation
1. Calculate 26 weeks backward and forward from current date
2. Adjust for timezone differences and daylight saving
3. Split large date ranges into manageable chunks
4. Handle future vs historical data differently

### Schedule Retrieval
1. Iterate through management units
2. Request schedules for each date chunk
3. Process shifts and activities separately
4. Handle schedule modifications and deletions

### Data Transformation
1. Convert Genesys schedule format to database schema
2. Calculate shift and activity durations
3. Resolve activity codes to descriptions
4. Apply timezone conversions

### Database Operations
1. Delete existing schedules for date range
2. Bulk insert new schedule data
3. Update sync tracking timestamps
4. Maintain referential integrity

## Monitoring

### Key Metrics
- **Schedules Processed**: Total number of agent schedules
- **Date Range Coverage**: Weeks of schedule data available
- **Activity Breakdown**: Distribution of activity types
- **Processing Time**: Time per management unit
- **Data Volume**: Records processed per execution

### Performance Indicators
- **API Response Time**: Average WFM API call duration
- **Chunk Processing Rate**: Date chunks processed per minute
- **Database Write Performance**: Bulk operation timing
- **Memory Usage**: Peak memory during processing

### Quality Metrics
- **Schedule Completeness**: Agents with complete schedules
- **Activity Coverage**: Percentage of time with activities
- **Data Consistency**: Validation error rates

## Troubleshooting

### Common Issues

#### Missing Schedule Data
- **Symptoms**: Empty or incomplete schedules for agents
- **Causes**: Unpublished schedules, permission issues, date range problems
- **Solutions**: Verify schedule publication, check WFM permissions, adjust date ranges

#### Timezone Issues
- **Symptoms**: Incorrect shift times, daylight saving problems
- **Causes**: Timezone configuration, DST transitions
- **Solutions**: Verify timezone settings, handle DST boundaries

#### Performance Problems
- **Symptoms**: Slow processing, API timeouts
- **Causes**: Large management units, wide date ranges
- **Solutions**: Reduce chunk sizes, process units separately

### Error Handling
- **API Rate Limits**: Automatic retry with backoff
- **Data Validation**: Skip invalid schedules with logging
- **Timezone Errors**: Default to UTC with warnings

## Integration with Other Systems

### Reporting Systems
- Schedule data feeds workforce analytics dashboards
- Provides baseline for adherence and productivity metrics
- Supports capacity planning and forecasting

### WFM Tools
- Integrates with third-party WFM solutions
- Provides schedule data for external analytics
- Supports schedule optimization workflows

## Performance Optimization

### Best Practices
- Process management units in parallel where possible
- Use appropriate chunk sizes for your environment
- Schedule during off-peak hours for large datasets
- Monitor API rate limits and adjust timing

### Scaling Considerations
- Large organizations may need longer processing windows
- Consider memory usage for organizations with many agents
- Monitor database performance during bulk operations

## Examples

### Basic Schedule Sync
```bash
GenesysAdapter --job WFMSchedule
```

### Historical Backfill
```bash
GenesysAdapter --job WFMSchedule --preferences.backfill true
```

### Specific Management Unit
```json
{
  "Job": "WFMSchedule",
  "Preferences": {
    "ManagementUnits": ["mu-id-1", "mu-id-2"]
  }
}
```

### Custom Date Range
```json
{
  "Job": "WFMSchedule",
  "Preferences": {
    "StartDate": "2024-01-01T00:00:00Z",
    "EndDate": "2024-12-31T23:59:59Z",
    "ChunkSize": 14
  }
}
```
