# OAuthUsage Job Documentation

## Overview

The **OAuthUsage** job synchronizes OAuth API usage data from Genesys Cloud, tracking API consumption, rate limiting events, and authentication patterns. This job provides critical insights into API utilization, helps optimize API usage strategies, and supports capacity planning and cost management initiatives.

## Data Flow Architecture

:::mermaid
graph TB
    subgraph "Genesys Cloud APIs"
        AUTH[OAuth Authentication<br/>/api/v2/oauth/token]
        USAGE[OAuth Usage API<br/>/api/v2/oauth/usage]
        CLIENTS[OAuth Clients<br/>/api/v2/oauth/clients]
        LIMITS[Rate Limits<br/>/api/v2/oauth/limits]
    end
    
    subgraph "OAuthUsage Job Process"
        OAUTH[GCUpdateOAuthUsage<br/>OAuth Usage Controller]
        FETCH[Data Fetcher<br/>Usage Data Retrieval]
        PROC[Usage Processor<br/>Metrics Calculation]
        ANALYZE[Usage Analyzer<br/>Pattern Analysis]
        AGGREGATE[Aggregator<br/>Summary Statistics]
    end
    
    subgraph "Database Tables"
        OUD[(oauthusagedata<br/>API Usage Records)]
        OUSD[(oauthusagesummarydata<br/>Usage Summaries)]
        ORLED[(oauthratelimitdata<br/>Rate Limit Events)]
        OCD[(oauthclientdata<br/>Client Information)]
    end
    
    subgraph "Usage Analytics"
        METRICS[Usage Metrics<br/>API Call Volumes]
        PATTERNS[Usage Patterns<br/>Temporal Analysis]
        LIMITS_TRACK[Rate Limiting<br/>Throttling Events]
        COSTS[Cost Analysis<br/>Usage-Based Billing]
    end
    
    %% Main flow
    OAUTH --> AUTH
    AUTH --> FETCH
    FETCH --> USAGE
    FETCH --> CLIENTS
    FETCH --> LIMITS
    USAGE --> PROC
    PROC --> ANALYZE
    ANALYZE --> AGGREGATE
    
    %% Analytics processing
    PROC --> METRICS
    PROC --> PATTERNS
    PROC --> LIMITS_TRACK
    PROC --> COSTS
    
    %% Database storage
    AGGREGATE --> OUD
    AGGREGATE --> OUSD
    LIMITS_TRACK --> ORLED
    FETCH --> OCD
    
    %% Styling
    classDef api fill:#e1f5fe
    classDef process fill:#e8f5e8
    classDef database fill:#fff3e0
    classDef analytics fill:#f3e5f5
    
    class AUTH,USAGE,CLIENTS,LIMITS api
    class OAUTH,FETCH,PROC,ANALYZE,AGGREGATE process
    class OUD,OUSD,ORLED,OCD database
    class METRICS,PATTERNS,LIMITS_TRACK,COSTS analytics
:::

## Configuration

### Basic Configuration
```json
{
  "Job": "OAuthUsage",
  "Preferences": {
    "MaxSyncSpan": "7.00:00",
    "IncludeRateLimitEvents": true,
    "TrackClientUsage": true,
    "AggregateHourly": true
  }
}
```

### OAuthUsage-Specific Options

#### Data Collection Scope
- **MaxSyncSpan**: Maximum time range for usage data collection
- **IncludeRateLimitEvents**: Track rate limiting events and throttling
- **TrackClientUsage**: Monitor usage by OAuth client
- **DetailedMetrics**: Include detailed API endpoint usage

#### Aggregation Settings
- **AggregateHourly**: Create hourly usage summaries
- **AggregateDaily**: Create daily usage summaries
- **ClientBreakdown**: Break down usage by OAuth client
- **EndpointAnalysis**: Analyze usage by API endpoint

## Database Schema

### Primary Tables

#### oauthusagedata
- **Purpose**: Detailed OAuth API usage records
- **Key Fields**: timestamp, clientid, endpoint, requestcount, responsetime
- **Metrics**: API calls, response times, error rates, data transfer
- **Indexes**: timestamp, clientid, endpoint

#### oauthusagesummarydata
- **Purpose**: Aggregated usage summaries by time period
- **Key Fields**: date, hour, clientid, totalrequests, averageresponsetime
- **Aggregations**: Hourly/daily totals, averages, peak usage
- **Indexes**: date, hour, clientid

#### oauthratelimitdata
- **Purpose**: Rate limiting events and throttling incidents
- **Key Fields**: timestamp, clientid, limittype, requestsremaining, resettime
- **Events**: Rate limit hits, throttling events, quota exhaustion
- **Indexes**: timestamp, clientid, limittype

#### oauthclientdata
- **Purpose**: OAuth client configuration and metadata
- **Key Fields**: clientid, clientname, clienttype, permissions, status
- **Information**: Client details, permissions, usage quotas
- **Indexes**: clientid, clientname, clienttype

## API Integration

### OAuth Usage APIs

#### OAuth Usage API
- **Endpoint**: `/api/v2/oauth/usage`
- **Purpose**: Retrieve API usage statistics and metrics
- **Granularity**: Hourly, daily, or custom time ranges
- **Metrics**: Request counts, response times, error rates

#### OAuth Clients API
- **Endpoint**: `/api/v2/oauth/clients`
- **Purpose**: Get OAuth client information and configuration
- **Data**: Client details, permissions, status, quotas

#### Rate Limits API
- **Endpoint**: `/api/v2/oauth/limits`
- **Purpose**: Retrieve current rate limit status and quotas
- **Information**: Current limits, remaining quota, reset times

### Query Parameters
```json
{
  "startDate": "2024-01-01T00:00:00Z",
  "endDate": "2024-01-07T23:59:59Z",
  "granularity": "PT1H",
  "clientId": "oauth-client-id"
}
```

## Usage Analytics Features

### API Usage Metrics
- **Request Volume**: Total API requests by time period
- **Response Times**: Average and percentile response times
- **Error Rates**: API error rates and failure patterns
- **Data Transfer**: Request/response payload sizes

### Rate Limiting Analysis
- **Throttling Events**: Rate limit hits and throttling incidents
- **Quota Utilization**: Usage against allocated quotas
- **Peak Usage**: Identification of peak usage periods
- **Limit Optimization**: Recommendations for limit adjustments

### Client Usage Patterns
- **Client Breakdown**: Usage analysis by OAuth client
- **Permission Usage**: API usage by permission scope
- **Temporal Patterns**: Usage patterns by time of day/week
- **Efficiency Metrics**: API efficiency and optimization opportunities

### Cost Analysis
- **Usage-Based Billing**: Cost calculations based on API usage
- **Trend Analysis**: Usage trends and growth patterns
- **Budget Forecasting**: Projected costs based on usage trends
- **Optimization Opportunities**: Cost reduction recommendations

## Dependencies

### Prerequisites
- **OAuth Configuration**: Valid OAuth client configuration
- **API Permissions**: OAuth usage read permissions
- **Database Schema**: OAuth usage tables must be installed

### No Job Dependencies
- **Independent**: OAuthUsage job operates independently
- **Self-Contained**: Provides its own authentication and data retrieval

## Performance Optimization

### Usage-Specific Optimizations
- **Time Range Management**: Optimize time ranges for API efficiency
- **Client Filtering**: Filter by specific OAuth clients when needed
- **Aggregation Strategy**: Use appropriate aggregation levels
- **Incremental Processing**: Only process new usage data

### Data Processing
- **Batch Aggregation**: Efficient aggregation of usage metrics
- **Parallel Processing**: Process multiple clients concurrently
- **Memory Management**: Optimize memory usage for large datasets

## Monitoring and Troubleshooting

### Key Metrics
- **Usage Records Processed**: Number of usage records synchronized
- **Rate Limit Events**: Number of rate limiting events detected
- **Client Coverage**: Percentage of OAuth clients with usage data
- **Data Completeness**: Coverage of usage data across time periods

### Common Issues

#### Missing Usage Data
- **Symptoms**: Gaps in usage data for specific time periods
- **Causes**: API permissions, client configuration, data retention
- **Solution**: Verify permissions and client configuration

#### Rate Limit Tracking Issues
- **Symptoms**: Rate limit events not captured or incorrect
- **Causes**: API response parsing, event detection logic
- **Solution**: Validate rate limit event detection and processing

#### Client Usage Discrepancies
- **Symptoms**: Usage data doesn't match expected client activity
- **Causes**: Client filtering, permission scope, data aggregation
- **Solution**: Review client filtering and aggregation logic

### OAuthUsage-Specific Logging
- **Processing Progress**: Log usage data processing status and counts
- **Rate Limit Events**: Log detected rate limiting events and details
- **Client Analysis**: Log client usage analysis and patterns

## Usage Examples

### Standard OAuth Usage Sync
```bash
GenesysAdapter --job OAuthUsage --config oauthusage-production.json
```

### Extended Historical Analysis
```json
{
  "Job": "OAuthUsage",
  "Preferences": {
    "MaxSyncSpan": "30.00:00",
    "IncludeRateLimitEvents": true,
    "DetailedMetrics": true
  }
}
```

### Client-Specific Usage Analysis
```json
{
  "Job": "OAuthUsage",
  "Preferences": {
    "TrackClientUsage": true,
    "ClientBreakdown": true,
    "EndpointAnalysis": true
  }
}
```

## Best Practices

### Usage Monitoring
- **Regular Collection**: Collect usage data regularly for trend analysis
- **Rate Limit Monitoring**: Monitor rate limiting events for optimization
- **Client Analysis**: Analyze usage patterns by OAuth client

### Performance Optimization
- **API Efficiency**: Use usage data to optimize API call patterns
- **Rate Limit Management**: Adjust usage patterns to avoid rate limiting
- **Cost Optimization**: Use usage data for cost optimization strategies

### Capacity Planning
- **Usage Forecasting**: Use historical data for capacity planning
- **Growth Analysis**: Track usage growth and plan for scaling
- **Quota Management**: Monitor quota utilization and plan adjustments

### Operational Excellence
- **Usage Dashboards**: Create dashboards for real-time usage monitoring
- **Alert Configuration**: Set up alerts for unusual usage patterns
- **Regular Reviews**: Conduct regular reviews of usage patterns and optimization opportunities
- **Documentation**: Maintain documentation of usage patterns and optimization strategies
