-- Conditional drop of subscriptiondata table if it exists and is empty
-- This table was used for legacy subscription data and is no longer needed
-- Only drop if the table exists and contains no records to preserve any existing data

DECLARE
    record_count INTEGER DEFAULT 0;
BEGIN
    -- Check if table exists and get record count
    SELECT COUNT(*) INTO :record_count
    FROM subscriptiondata
    WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'SUBSCRIP<PERSON>ONDATA');

    IF (:record_count = 0) THEN
        DROP TABLE IF EXISTS subscriptiondata;
        RETURN 'Dropped empty subscriptiondata table';
    ELSE
        RETURN 'Preserved subscriptiondata table - contains ' || :record_count || ' records';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        -- Table doesn't exist, nothing to do
        RETURN 'subscriptiondata table does not exist';
END;