IF dbo.csg_table_exists('licenseuserdata') = 0
CREATE TABLE [licenseuserdata](
    [keyid] [nvarchar](100) NOT NULL,
    [userid] [nvarchar](50),
    [username] [nvarchar](200),
    [email] [nvarchar](200),
    [licensename] [nvarchar](200),
    [licensetype] [nvarchar](100),
    [licenseassigneddate] [datetime],
    [licenseexpirydate] [datetime],
    [licensestatus] [nvarchar](50),
    [organizationid] [nvarchar](50),
    [divisionid] [nvarchar](50),
    [updated] [datetime],
    CONSTRAINT [PK_licenseuserdata] PRIMARY KEY ([keyid])
);

-- Create indexes for better query performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_licenseuserdata_userid' AND object_id = OBJECT_ID('licenseuserdata'))
    CREATE INDEX IX_licenseuserdata_userid ON licenseuserdata (userid);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_licenseuserdata_licensename' AND object_id = OBJECT_ID('licenseuserdata'))
    CREATE INDEX IX_licenseuserdata_licensename ON licenseuserdata (licensename);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_licenseuserdata_updated' AND object_id = OBJECT_ID('licenseuserdata'))
    CREATE INDEX IX_licenseuserdata_updated ON licenseuserdata (updated);
