-- License user data from Genesys Cloud /api/v2/license/users endpoint
-- NOTE: This API only provides userid, licensename, and selfUri
-- The following fields will always be NULL due to API limitations:
-- username, email, licensetype, licenseassigneddate, licenseexpirydate, licensestatus, organizationid, divisionid

IF dbo.csg_table_exists('licenseuserdata') = 0
CREATE TABLE [licenseuserdata](
    [keyid] [nvarchar](100) NOT NULL,
    [userid] [nvarchar](50),                    -- From API: user ID
    [username] [nvarchar](200),                 -- Always NULL - not provided by API
    [email] [nvarchar](200),                    -- Always NULL - not provided by API
    [licensename] [nvarchar](200),              -- From API: license name
    [licensetype] [nvarchar](100),              -- Always NULL - not provided by API
    [licenseassigneddate] [datetime],           -- Always NULL - not provided by API
    [licenseexpirydate] [datetime],             -- Always NULL - not provided by API
    [licensestatus] [nvarchar](50),             -- Always NULL - not provided by API
    [organizationid] [nvarchar](50),            -- Always NULL - not provided by API
    [divisionid] [nvarchar](50),                -- Always NULL - not provided by API
    [updated] [datetime],                       -- Populated by adapter
    CONSTRAINT [PK_licenseuserdata] PRIMARY KEY ([keyid])
);

-- Create indexes for better query performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_licenseuserdata_userid' AND object_id = OBJECT_ID('licenseuserdata'))
    CREATE INDEX IX_licenseuserdata_userid ON licenseuserdata (userid);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_licenseuserdata_licensename' AND object_id = OBJECT_ID('licenseuserdata'))
    CREATE INDEX IX_licenseuserdata_licensename ON licenseuserdata (licensename);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_licenseuserdata_updated' AND object_id = OBJECT_ID('licenseuserdata'))
    CREATE INDEX IX_licenseuserdata_updated ON licenseuserdata (updated);
