# QueueMembership Job Documentation

## Overview

The **QueueMembership** job synchronizes queue membership data from Genesys Cloud, tracking which agents are assigned to which queues and their membership status. This job is essential for understanding agent-queue relationships, routing capabilities, and organizational structure within the contact center.

## Data Flow Architecture

:::mermaid
graph TB
    subgraph "Genesys Cloud APIs"
        AUTH[OAuth Authentication<br/>/api/v2/oauth/token]
        QUEUES[Queues API<br/>/api/v2/routing/queues]
        MEMBERS[Queue Members<br/>/api/v2/routing/queues/{id}/members]
        USERS[Users API<br/>/api/v2/users]
    end
    
    subgraph "QueueMembership Job Process"
        QM[GCUpdateQueueMembership<br/>Membership Controller]
        FETCH[Data Fetcher<br/>Queue and Member Retrieval]
        PROC[Membership Processor<br/>Relationship Analysis]
        VALIDATE[Data Validator<br/>Membership Validation]
        DIFF[Diffing Engine<br/>Change Detection]
    end
    
    subgraph "Database Tables"
        QMD[(queuemembershipdata<br/>Queue-Agent Relationships)]
        QD[(queuedetails<br/>Queue Information)]
        UD[(userdetails<br/>User Information)]
        QMHD[(queuemembershiphistorydata<br/>Membership History)]
    end
    
    subgraph "Membership Features"
        STATUS[Membership Status<br/>Active/Inactive Tracking]
        SKILLS[Skill Requirements<br/>Queue Skill Mapping]
        ROUTING[Routing Rules<br/>Assignment Logic]
        AUDIT[Audit Trail<br/>Membership Changes]
    end
    
    %% Main flow
    QM --> AUTH
    AUTH --> FETCH
    FETCH --> QUEUES
    FETCH --> MEMBERS
    FETCH --> USERS
    MEMBERS --> PROC
    PROC --> VALIDATE
    VALIDATE --> DIFF
    
    %% Membership processing
    PROC --> STATUS
    PROC --> SKILLS
    PROC --> ROUTING
    PROC --> AUDIT
    
    %% Database storage
    DIFF --> QMD
    STATUS --> QMD
    AUDIT --> QMHD
    
    %% Reference data
    QD -.-> VALIDATE
    UD -.-> VALIDATE
    
    %% Styling
    classDef api fill:#e1f5fe
    classDef process fill:#e8f5e8
    classDef database fill:#fff3e0
    classDef feature fill:#f3e5f5
    
    class AUTH,QUEUES,MEMBERS,USERS api
    class QM,FETCH,PROC,VALIDATE,DIFF process
    class QMD,QD,UD,QMHD database
    class STATUS,SKILLS,ROUTING,AUDIT feature
:::

## Configuration

### Basic Configuration
```json
{
  "Job": "QueueMembership",
  "Preferences": {
    "IncludeInactiveMembers": false,
    "TrackMembershipHistory": true,
    "ValidateSkillRequirements": true
  }
}
```

### QueueMembership-Specific Options

#### Membership Scope
- **IncludeInactiveMembers**: Include inactive queue members (default: false)
- **ActiveQueuesOnly**: Only process active queues (default: true)
- **SkillBasedRouting**: Include skill-based routing information

#### Historical Tracking
- **TrackMembershipHistory**: Maintain membership change history
- **HistoryRetentionDays**: Number of days to retain membership history
- **AuditChanges**: Track all membership changes with timestamps

## Database Schema

### Primary Tables

#### queuemembershipdata
- **Purpose**: Current queue-agent membership relationships
- **Key Fields**: queueid, userid, membershipstatus, joindate, leavedate
- **Features**: Active membership tracking, skill requirements
- **Indexes**: queueid, userid, membershipstatus

#### queuemembershiphistorydata
- **Purpose**: Historical record of membership changes
- **Key Fields**: queueid, userid, changedate, changetype, previousstatus
- **Features**: Audit trail, membership timeline, change tracking
- **Indexes**: queueid, userid, changedate

#### Queue Reference Tables
- **queuedetails**: Queue configuration and metadata
- **userdetails**: Agent information and attributes
- **skilldetails**: Skill definitions and requirements

## API Integration

### Queue Membership APIs

#### Queues API
- **Endpoint**: `/api/v2/routing/queues`
- **Purpose**: Retrieve list of all queues
- **Filters**: Active queues, queue types, divisions

#### Queue Members API
- **Endpoint**: `/api/v2/routing/queues/{queueId}/members`
- **Purpose**: Get members for specific queue
- **Data**: Member list, membership status, join dates

#### Users API Integration
- **Endpoint**: `/api/v2/users`
- **Purpose**: Validate user information and status
- **Cross-Reference**: Link queue members to user details

### Query Parameters
```json
{
  "pageSize": 100,
  "pageNumber": 1,
  "expand": ["skills", "division"],
  "name": "queue-name-filter"
}
```

## Membership Management Features

### Membership Status Tracking
- **Active Members**: Currently assigned and active queue members
- **Inactive Members**: Temporarily inactive or suspended members
- **Pending Members**: Members with pending activation
- **Historical Members**: Previously assigned members (if tracking enabled)

### Skill-Based Routing
- **Skill Requirements**: Track required skills for queue membership
- **Skill Validation**: Validate agent skills against queue requirements
- **Skill Gaps**: Identify skill gaps in queue coverage
- **Skill Mapping**: Map agent skills to queue requirements

### Routing Configuration
- **Routing Rules**: Queue-specific routing rules and priorities
- **Agent Priorities**: Agent priority within queue assignments
- **Overflow Rules**: Overflow and escalation routing configuration
- **Time-Based Rules**: Time-of-day routing variations

## Dependencies

### Prerequisites
- **FactData**: Queue and user reference data must be current
- **Database Schema**: Queue membership tables must be installed
- **API Permissions**: Routing and users read permissions

### Related Jobs
- **FactData**: Provides baseline queue and user information
- **UserQueueMapping**: Related user-queue relationship tracking
- **UserQueueAudit**: Audit trail for queue assignment changes
- **Realtime**: Real-time queue membership status

## Performance Optimization

### Membership-Specific Optimizations
- **Queue Batching**: Process queues in batches to optimize API usage
- **Member Caching**: Cache member data to reduce redundant API calls
- **Incremental Updates**: Only process changed memberships
- **Parallel Processing**: Process multiple queues concurrently

### Data Processing
- **Bulk Operations**: Use bulk database operations for membership updates
- **Change Detection**: Efficient detection of membership changes
- **History Management**: Optimize historical data storage and retrieval

## Monitoring and Troubleshooting

### Key Metrics
- **Membership Records Processed**: Number of membership relationships synchronized
- **Queue Coverage**: Percentage of queues with complete membership data
- **Member Validation**: Success rate of member validation
- **Change Detection**: Number of membership changes detected

### Common Issues

#### Missing Queue Members
- **Symptoms**: Queues showing no members or incomplete member lists
- **Causes**: API permissions, queue configuration, member status filters
- **Solution**: Verify API permissions and queue member API responses

#### Membership Status Inconsistencies
- **Symptoms**: Member status doesn't match Genesys Cloud UI
- **Causes**: Status synchronization timing, API response delays
- **Solution**: Verify status mapping and implement proper retry logic

#### Skill Validation Errors
- **Symptoms**: Skill requirement validation failures
- **Causes**: Missing skill data, skill definition changes
- **Solution**: Ensure skill reference data is current and complete

### QueueMembership-Specific Logging
- **Processing Progress**: Log queue processing status and member counts
- **Membership Changes**: Log detected membership changes and updates
- **Validation Results**: Log membership validation results and any issues

## Usage Examples

### Standard Queue Membership Sync
```bash
GenesysAdapter --job QueueMembership --config queuemembership-production.json
```

### Include Inactive Members
```json
{
  "Job": "QueueMembership",
  "Preferences": {
    "IncludeInactiveMembers": true,
    "TrackMembershipHistory": true
  }
}
```

### Skill-Based Routing Focus
```json
{
  "Job": "QueueMembership",
  "Preferences": {
    "ValidateSkillRequirements": true,
    "IncludeSkillMapping": true
  }
}
```

## Best Practices

### Membership Data Management
- **Regular Sync**: Run queue membership job regularly to maintain current data
- **Change Tracking**: Enable membership history tracking for audit purposes
- **Validation**: Regularly validate membership data against source systems

### Performance Optimization
- **Batch Processing**: Use appropriate batch sizes for queue processing
- **Incremental Updates**: Focus on changed memberships to improve efficiency
- **Caching Strategy**: Implement effective caching for reference data

### Data Quality
- **Membership Validation**: Validate membership relationships for consistency
- **Skill Verification**: Verify skill requirements and agent capabilities
- **Status Monitoring**: Monitor membership status changes and patterns

### Operational Considerations
- **Queue Coverage**: Ensure all active queues have appropriate member coverage
- **Skill Distribution**: Monitor skill distribution across queue memberships
- **Change Management**: Track and manage queue membership changes effectively

### Reporting and Analytics
- **Membership Reports**: Generate reports on queue membership distribution
- **Skill Analysis**: Analyze skill coverage and gaps across queues
- **Change Analytics**: Track membership change patterns and trends
- **Coverage Metrics**: Monitor queue coverage and agent distribution
