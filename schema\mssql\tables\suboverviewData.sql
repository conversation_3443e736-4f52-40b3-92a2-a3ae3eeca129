IF dbo.csg_table_exists('suboverviewData') = 0
CREATE TABLE [suboverviewData](
    [keyid] [nvarchar](200) NOT NULL,
    [rowdate] [datetime],
    [startdate] [datetime],
    [enddate] [datetime],
    [licname] [nvarchar](200),
    [partnumber] [nvarchar](50),
    [grouping] [nvarchar](50),
    [unitofmeasuretype] [nvarchar](50),
    [usagequantity] [decimal](20, 2),
    [prepayQuantity] [decimal](20, 2),
    [overageprice] [decimal](20, 2),
    [iscancellable] [bit],
    [bundlequantity] [int],
    [isthirdparty] [bit],
    [updated] [datetime],
    CONSTRAINT [PK_suboverviewData] PRIMARY KEY ([keyid])
);

IF dbo.csg_column_exists('suboverviewData', 'usagequantity') = 0
    ALTER TABLE suboverviewData ADD usagequantity DECIMAL(20, 2);
ELSE
    ALTER TABLE suboverviewData ALTER COLUMN usagequantity DECIMAL(20, 10);

IF dbo.csg_column_exists('suboverviewData', 'startdate') = 0
    ALTER TABLE suboverviewData ADD startdate datetime;
ELSE
    ALTER TABLE suboverviewData ALTER COLUMN startdate datetime;

IF dbo.csg_column_exists('suboverviewData', 'enddate') = 0
    ALTER TABLE suboverviewData ADD enddate datetime;
ELSE
    ALTER TABLE suboverviewData ALTER COLUMN enddate datetime;

IF dbo.csg_column_exists('suboverviewData', 'prepayQuantity') = 0
    ALTER TABLE suboverviewData ADD prepayQuantity DECIMAL(20, 2);
ELSE
    ALTER TABLE suboverviewData ALTER COLUMN prepayQuantity DECIMAL(20, 10);

IF EXISTS (
    SELECT 1 
    FROM suboverviewData
    WHERE startdate IS NULL
)
BEGIN
    TRUNCATE TABLE suboverviewdata;
    UPDATE tabledefinitions
    SET datekeyfield = DATEADD(month, -12, GETDATE()) 
    WHERE tablename = 'suboverviewdata';
END
ELSE
BEGIN
    PRINT 'No records found with startdate as NULL.';
END

BEGIN
    IF EXISTS (
        SELECT 1
        FROM suboverviewdata
        WHERE LEN(keyid) - LEN(REPLACE(keyid, '|', '')) < 3
    )
    BEGIN
        DELETE FROM suboverviewdata
        WHERE LEN(keyid) - LEN(REPLACE(keyid, '|', '')) < 3;
    END
END;