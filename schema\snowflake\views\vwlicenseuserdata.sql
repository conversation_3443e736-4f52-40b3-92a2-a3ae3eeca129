CREATE OR REPLACE VIEW vwlicenseuserdata AS
SELECT
    lud.keyid,
    lud.userid,
    lud.licensename,
    lud.updated,
    -- User details from vwUserDetail
    ud.name AS username,
    ud.email,
    ud.jabberid,
    ud.state AS userstate,
    ud.title,
    ud.department,
    ud.employeeid,
    ud.datehire,
    -- Manager information
    ud.managerid,
    ud.managername,
    -- Division information
    ud.divisionid,
    ud.divisionname,
    ud.homedivision
FROM
    licenseuserdata lud
    LEFT JOIN vwUserDetail ud ON lud.userid = ud.id;
